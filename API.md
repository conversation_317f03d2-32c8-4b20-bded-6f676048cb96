# FIM Command-Line Interface (API) Reference

This document provides comprehensive documentation for the `fim-ctl` command-line interface.

## Overview

`fim-ctl` is the primary command-line tool for managing the FIM (File Integrity Monitoring) system. It provides functionality to start, stop, test, and manage FIM monitoring operations.

## Synopsis

```
fim-ctl <command> [options] [config_file]
```

## Global Options

- **`--help`** - Display help information
- **`--version`** - Display version information

## Commands

### start

Start FIM monitoring with the specified configuration.

#### Syntax
```bash
fim-ctl start <config_file>
```

#### Parameters
- **`config_file`** (required): Path to YAML configuration file

#### Description
Starts the FIM monitoring system in foreground mode. The process will:
1. Parse and validate the configuration file
2. Load eBPF programs into the kernel
3. Attach LSM hooks for file monitoring
4. Begin real-time event processing
5. Log access events to stdout/stderr

#### Examples
```bash
# Start with default configuration
sudo fim-ctl start /etc/fim/policy.yaml

# Start with custom configuration
sudo fim-ctl start /path/to/custom-config.yaml

# Start with example configuration
sudo fim-ctl start examples/configs/basic_example.yaml
```

#### Output
```
[INFO] Starting FIM with new configuration format: /etc/fim/policy.yaml
[INFO] Parsing new YAML config: /etc/fim/policy.yaml
[INFO] ✓ Added file_access rule: path=/etc/passwd, access=write, action=block
[INFO] ✓ Loaded file_access config: rule_id=system_protection, enabled=true
[INFO] Configuration validation passed: 1 rules
[INFO] Loading eBPF program: build/fim_lsm.o
[INFO] eBPF program loaded successfully
[INFO] ✓ Attached LSM program: fim_file_open
[INFO] Starting event processing...
```

#### Runtime Events
During operation, FIM will log access events:
```
[2024-01-15 10:30:45] WRITE BLOCK /etc/passwd by user (PID: 12345, inode: 67890)
[2024-01-15 10:30:46] READ AUDIT /home/<USER>/document.txt by user (PID: 12346, inode: 67891)
```

### test

Validate configuration file syntax and rules without starting monitoring.

#### Syntax
```bash
fim-ctl test <config_file>
```

#### Parameters
- **`config_file`** (required): Path to YAML configuration file to validate

#### Description
Performs comprehensive validation of the configuration file:
1. YAML syntax validation
2. Required field verification
3. Rule logic validation
4. File path accessibility checks
5. Configuration summary display

#### Examples
```bash
# Test default configuration
sudo fim-ctl test /etc/fim/policy.yaml

# Test custom configuration
sudo fim-ctl test examples/configs/basic_example.yaml
```

#### Output (Success)
```
Testing YAML configuration format...
[INFO] Parsing new YAML config: examples/configs/basic_example.yaml
[INFO] ✓ Added file_access rule: path=/etc/passwd, access=write, action=block
[INFO] ✓ Loaded file_access config: rule_id=system_protection, enabled=true
[INFO] Configuration validation passed: 1 rules

=== Configuration Summary ===
File Access Rules: 1
  Read rules:      0
  Write rules:     1
  Block actions:   1
  Audit actions:   0
Filesystem Protect: Disabled
=============================

✓ Configuration test passed
```

#### Output (Error)
```
Testing YAML configuration format...
[ERROR] YAML parsing failed: Invalid syntax at line 15
✗ Configuration test failed
```

## Exit Codes

FIM uses standard exit codes to indicate operation status:

- **`0`** - Success
- **`1`** - General error
- **`2`** - Configuration error
- **`3`** - eBPF loading error
- **`4`** - Permission error
- **`5`** - File I/O error
- **`6`** - Invalid parameters

## Error Handling

### Common Errors

#### Permission Denied
```
[ERROR] Failed to load eBPF program: Permission denied
```
**Cause**: Insufficient privileges
**Solution**: Run with `sudo` or as root user

#### Configuration File Not Found
```
[ERROR] Failed to open configuration file: No such file or directory
```
**Cause**: Invalid file path
**Solution**: Verify file path and permissions

#### Invalid Configuration
```
[ERROR] Invalid action: 'monitor', must be 'block' or 'audit'
```
**Cause**: Configuration syntax error
**Solution**: Fix configuration file syntax

#### eBPF Not Supported
```
[ERROR] BPF LSM not supported on this kernel
```
**Cause**: Kernel lacks eBPF LSM support
**Solution**: Upgrade kernel or enable BPF LSM

### Debug Mode

Enable detailed logging for troubleshooting:

```bash
export FIM_DEBUG=1
sudo fim-ctl start /etc/fim/policy.yaml
```

## Signal Handling

FIM responds to the following signals:

- **`SIGINT` (Ctrl+C)** - Graceful shutdown
- **`SIGTERM`** - Graceful shutdown
- **`SIGHUP`** - Log message (configuration reload not supported)

### Graceful Shutdown
```
^C[INFO] Received signal 2, shutting down...
[INFO] Cleaning up resources...
[INFO] Cleanup completed
```

## Integration Examples

### Systemd Service

Create a systemd service file:

```ini
[Unit]
Description=File Integrity Monitoring
After=network.target

[Service]
Type=simple
ExecStart=/usr/local/bin/fim-ctl start /etc/fim/policy.yaml
ExecStop=/bin/kill -TERM $MAINPID
Restart=always
RestartSec=5
User=root

[Install]
WantedBy=multi-user.target
```

### Shell Script Integration

```bash
#!/bin/bash

CONFIG_FILE="/etc/fim/policy.yaml"
LOG_FILE="/var/log/fim.log"

# Test configuration before starting
if fim-ctl test "$CONFIG_FILE"; then
    echo "Configuration valid, starting FIM..."
    fim-ctl start "$CONFIG_FILE" >> "$LOG_FILE" 2>&1 &
    FIM_PID=$!
    echo "FIM started with PID: $FIM_PID"
else
    echo "Configuration test failed, aborting startup"
    exit 1
fi
```

### Monitoring Script

```bash
#!/bin/bash

# Monitor FIM process and restart if needed
while true; do
    if ! pgrep -f "fim-ctl start" > /dev/null; then
        echo "FIM not running, restarting..."
        fim-ctl start /etc/fim/policy.yaml &
    fi
    sleep 60
done
```

## Performance Considerations

### Resource Usage

FIM resource usage depends on:
- Number of monitored files/directories
- File system activity volume
- Configuration complexity
- System specifications

### Optimization Tips

1. **Minimize Monitored Paths**: Only monitor essential files/directories
2. **Use Specific Paths**: Avoid broad directory monitoring when possible
3. **Choose Actions Wisely**: Use `audit` for high-frequency operations
4. **Monitor Resource Usage**: Check CPU and memory consumption

### Monitoring Performance

```bash
# Check FIM process resource usage
ps aux | grep fim-ctl

# Monitor eBPF program statistics
sudo bpftool prog show | grep fim

# Check system load
top -p $(pgrep fim-ctl)
```

## Troubleshooting

### Verbose Logging

Enable debug output for detailed troubleshooting:

```bash
export FIM_DEBUG=1
sudo fim-ctl start /etc/fim/policy.yaml
```

### Log Analysis

Common log patterns to look for:

```bash
# Successful rule loading
grep "Added file_access rule" /var/log/fim.log

# Access events
grep -E "(BLOCK|AUDIT)" /var/log/fim.log

# Error messages
grep "ERROR" /var/log/fim.log
```

### System Integration

Check system integration:

```bash
# Verify eBPF programs are loaded
sudo bpftool prog list | grep fim

# Check LSM hooks
sudo bpftool link list | grep lsm

# Monitor system calls
sudo strace -p $(pgrep fim-ctl)
```

## Advanced Usage

### Configuration Validation Pipeline

```bash
#!/bin/bash
# validate-fim-config.sh

CONFIG_FILE="$1"

if [ -z "$CONFIG_FILE" ]; then
    echo "Usage: $0 <config_file>"
    exit 1
fi

echo "Validating FIM configuration: $CONFIG_FILE"

# Test configuration
if fim-ctl test "$CONFIG_FILE"; then
    echo "✓ Configuration is valid"

    # Additional checks
    echo "Checking file paths..."
    grep -o 'path: "[^"]*"' "$CONFIG_FILE" | cut -d'"' -f2 | while read path; do
        if [ ! -e "$path" ]; then
            echo "⚠ Warning: Path does not exist: $path"
        else
            echo "✓ Path exists: $path"
        fi
    done

    exit 0
else
    echo "✗ Configuration validation failed"
    exit 1
fi
```

### Automated Deployment

```bash
#!/bin/bash
# deploy-fim.sh

set -e

CONFIG_FILE="/etc/fim/policy.yaml"
BACKUP_DIR="/etc/fim/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Create backup
mkdir -p "$BACKUP_DIR"
if [ -f "$CONFIG_FILE" ]; then
    cp "$CONFIG_FILE" "$BACKUP_DIR/policy_${TIMESTAMP}.yaml"
    echo "Backup created: $BACKUP_DIR/policy_${TIMESTAMP}.yaml"
fi

# Deploy new configuration
cp "$1" "$CONFIG_FILE"

# Test new configuration
if fim-ctl test "$CONFIG_FILE"; then
    echo "New configuration deployed successfully"

    # Restart service if running
    if systemctl is-active --quiet fim; then
        echo "Restarting FIM service..."
        systemctl restart fim
        echo "Service restarted"
    fi
else
    echo "Configuration test failed, rolling back..."
    if [ -f "$BACKUP_DIR/policy_${TIMESTAMP}.yaml" ]; then
        cp "$BACKUP_DIR/policy_${TIMESTAMP}.yaml" "$CONFIG_FILE"
        echo "Rollback completed"
    fi
    exit 1
fi
```

### Log Processing

```bash
#!/bin/bash
# process-fim-logs.sh

LOG_FILE="/var/log/fim.log"
OUTPUT_DIR="/var/log/fim/processed"
DATE=$(date +%Y-%m-%d)

mkdir -p "$OUTPUT_DIR"

# Extract blocked events
grep "BLOCK" "$LOG_FILE" > "$OUTPUT_DIR/blocked_${DATE}.log"

# Extract audit events
grep "AUDIT" "$LOG_FILE" > "$OUTPUT_DIR/audit_${DATE}.log"

# Generate summary
echo "FIM Log Summary for $DATE" > "$OUTPUT_DIR/summary_${DATE}.txt"
echo "=========================" >> "$OUTPUT_DIR/summary_${DATE}.txt"
echo "Blocked events: $(wc -l < "$OUTPUT_DIR/blocked_${DATE}.log")" >> "$OUTPUT_DIR/summary_${DATE}.txt"
echo "Audit events: $(wc -l < "$OUTPUT_DIR/audit_${DATE}.log")" >> "$OUTPUT_DIR/summary_${DATE}.txt"

# Top processes
echo "" >> "$OUTPUT_DIR/summary_${DATE}.txt"
echo "Top processes by events:" >> "$OUTPUT_DIR/summary_${DATE}.txt"
grep -E "(BLOCK|AUDIT)" "$LOG_FILE" | \
    grep -o 'PID: [0-9]*' | \
    sort | uniq -c | sort -nr | head -10 >> "$OUTPUT_DIR/summary_${DATE}.txt"
```

## API Reference Summary

| Command | Purpose | Requires Root | Config File | Exit Codes |
|---------|---------|---------------|-------------|------------|
| `start` | Start monitoring | Yes | Required | 0, 1-6 |
| `test` | Validate config | Yes | Required | 0, 1-2, 5-6 |

## Environment Variables

- **`FIM_DEBUG`** - Enable debug logging (set to any value)
- **`FIM_CONFIG_DIR`** - Default configuration directory (default: `/etc/fim`)

## Files and Directories

- **`/usr/local/bin/fim-ctl`** - Main executable
- **`/etc/fim/policy.yaml`** - Default configuration file
- **`/etc/systemd/system/fim.service`** - Systemd service file
- **`/var/log/fim.log`** - Default log file (when using systemd)

## Best Practices

1. **Always test configurations** before deployment
2. **Use version control** for configuration files
3. **Monitor resource usage** in production
4. **Implement log rotation** for long-running deployments
5. **Create backup procedures** for configuration files
6. **Document custom rules** and their purposes
