#include "ebpf_manager.h"
#include "utils.h"

/* ========== Static Variables ========== */

/* Map file descriptors */
static int file_access_map_fd = -1;
static int file_access_exceptions_fd = -1;
static int filesystem_protect_config_fd = -1;
static int filesystem_exceptions_fd = -1;

/* ========== eBPF Program Management ========== */

int ebpf_load_program(const char *obj_file) {
    if (!obj_file) {
        return FIM_ERROR_INVALID_PARAM;
    }

    FIM_LOG_INFO("Loading eBPF program: %s", obj_file);

    /* Open eBPF object file */
    bpf_obj = bpf_object__open(obj_file);
    if (libbpf_get_error(bpf_obj)) {
        FIM_LOG_ERROR("Failed to open eBPF object file: %s", obj_file);
        return FIM_ERROR_EBPF;
    }

    /* Load eBPF program */
    int err = bpf_object__load(bpf_obj);
    if (err) {
        FIM_LOG_ERROR("Failed to load eBPF program: %s", strerror(-err));
        bpf_object__close(bpf_obj);
        bpf_obj = NULL;
        return FIM_ERROR_EBPF;
    }

    /* Initialize maps */
    int result = ebpf_init_maps();
    if (result != FIM_SUCCESS) {
        FIM_LOG_ERROR("Failed to initialize eBPF maps");
        bpf_object__close(bpf_obj);
        bpf_obj = NULL;
        return result;
    }

    FIM_LOG_INFO("eBPF program loaded successfully");
    return FIM_SUCCESS;
}

int ebpf_attach_programs(void) {
    if (!bpf_obj) {
        FIM_LOG_ERROR("eBPF object not loaded");
        return FIM_ERROR_EBPF;
    }

    const char *lsm_programs[] = {
        "fim_file_open",
        "fim_inode_create",
        "fim_inode_unlink",
        "fim_inode_rename",
        "fim_inode_mkdir",
        "fim_inode_rmdir",
        "fim_inode_setattr",
        "fim_inode_link",
        "fim_inode_symlink"
    };

    int attached_count = 0;

    for (int i = 0; i < 9; i++) {
        struct bpf_program *prog = bpf_object__find_program_by_name(bpf_obj, lsm_programs[i]);
        if (!prog) {
            FIM_LOG_ERROR("Failed to find program: %s", lsm_programs[i]);
            continue;
        }

        struct bpf_link *link = bpf_program__attach(prog);
        if (libbpf_get_error(link)) {
            FIM_LOG_ERROR("Failed to attach program: %s", lsm_programs[i]);
            continue;
        }

        if (link_count < MAX_LINKS) {
            links[link_count++] = link;
            FIM_LOG_INFO("✓ Attached LSM program: %s", lsm_programs[i]);
            attached_count++;
        } else {
            FIM_LOG_ERROR("Too many links, cannot attach: %s", lsm_programs[i]);
            bpf_link__destroy(link);
        }
    }

    return attached_count > 0 ? FIM_SUCCESS : FIM_ERROR_EBPF;
}

void ebpf_cleanup(void) {

    // Detach all programs
    for (int i = 0; i < link_count; i++) {
        if (links[i]) {
            bpf_link__destroy(links[i]);
            links[i] = NULL;
        }
    }
    link_count = 0;

    // Close eBPF object
    if (bpf_obj) {
        bpf_object__close(bpf_obj);
        bpf_obj = NULL;
    }

    // Reset map file descriptors
    monitored_filenames_fd = inode_events_fd = -1;
}

int ebpf_is_loaded(void) {
    return bpf_obj != NULL ? 1 : 0;
}

/* ========== eBPF Map Management ========== */

int ebpf_init_maps(void) {
    if (!bpf_obj) {
        return FIM_ERROR_EBPF;
    }



    // Legacy path_inode_fd and inode_path_fd removed
    monitored_filenames_fd = bpf_object__find_map_fd_by_name(bpf_obj, "monitored_filenames_map");
    inode_events_fd = bpf_object__find_map_fd_by_name(bpf_obj, "inode_events");

    struct bpf_map *map;
    FIM_LOG_INFO("Available maps in eBPF object:");
    bpf_object__for_each_map(map, bpf_obj) {
        const char *map_name = bpf_map__name(map);
        int map_fd = bpf_map__fd(map);
        FIM_LOG_INFO("  Map: %s (fd: %d)", map_name, map_fd);
    }

    // Initialize new configuration maps
    file_access_map_fd = bpf_object__find_map_fd_by_name(bpf_obj, "file_access_map");
    file_access_exceptions_fd = bpf_object__find_map_fd_by_name(bpf_obj, "file_access_exc");
    filesystem_protect_config_fd = bpf_object__find_map_fd_by_name(bpf_obj, "filesystem_prot");
    filesystem_exceptions_fd = bpf_object__find_map_fd_by_name(bpf_obj, "filesystem_exce");



    // Check critical maps
    if (monitored_filenames_fd < 0) {
        FIM_LOG_ERROR("Failed to find monitored_filenames_map");
        return FIM_ERROR_EBPF;
    }

    // Check new configuration maps
    if (file_access_map_fd < 0) {
        FIM_LOG_ERROR("Failed to find file_access_map (fd: %d)", file_access_map_fd);
        return FIM_ERROR_EBPF;
    }

    if (file_access_exceptions_fd < 0) {
        FIM_LOG_ERROR("Failed to find file_access_exceptions (fd: %d)", file_access_exceptions_fd);
        return FIM_ERROR_EBPF;
    }

    if (filesystem_protect_config_fd < 0) {
        FIM_LOG_ERROR("Failed to find filesystem_protect_config (fd: %d)", filesystem_protect_config_fd);
        return FIM_ERROR_EBPF;
    }

    if (filesystem_exceptions_fd < 0) {
        FIM_LOG_ERROR("Failed to find filesystem_exceptions (fd: %d)", filesystem_exceptions_fd);
        return FIM_ERROR_EBPF;
    }


    FIM_LOG_INFO("✓ All new configuration maps found and initialized");

    return FIM_SUCCESS;
}

int ebpf_clear_maps(void) {
    // Legacy maps are no longer used - only clear monitored filenames

    // Clear monitored filenames map
    if (monitored_filenames_fd >= 0) {
        char filename_key[64];
        char next_filename_key[64];
        int found = 0;

        while (bpf_map_get_next_key(monitored_filenames_fd, found ? filename_key : NULL, next_filename_key) == 0) {
            bpf_map_delete_elem(monitored_filenames_fd, next_filename_key);
            memcpy(filename_key, next_filename_key, sizeof(filename_key));
            found = 1;
        }
    }

    return FIM_SUCCESS;
}

int ebpf_get_map_fd(const char *map_name) {
    if (!bpf_obj || !map_name) {
        return -1;
    }

    return bpf_object__find_map_fd_by_name(bpf_obj, map_name);
}

/* ========== Program Information ========== */

void ebpf_print_status(void) {
    printf("\n=== eBPF Status ===\n");
    printf("Program loaded: %s\n", ebpf_is_loaded() ? "Yes" : "No");
    printf("Attached programs: %d\n", link_count);

    if (ebpf_is_loaded()) {
        printf("Map file descriptors:\n");
        printf("  monitored_filenames_fd: %d\n", monitored_filenames_fd);
        printf("  inode_events_fd: %d\n", inode_events_fd);
        printf("  file_access_map_fd: %d\n", file_access_map_fd);
        printf("  filesystem_protect_config_fd: %d\n", filesystem_protect_config_fd);
    }
    printf("==================\n\n");
}

/* ========== Rule Management ========== */

// Legacy function - no longer used with new architecture
// Rules are now applied through ebpf_apply_new_config()

int ebpf_apply_rules(void) {
    if (!ebpf_is_loaded()) {
        FIM_LOG_ERROR("eBPF program not loaded");
        return FIM_ERROR_EBPF;
    }

    // Clear existing maps
    ebpf_clear_maps();

    // Legacy rule application is no longer supported
    // Use ebpf_apply_new_config() instead
    FIM_LOG_ERROR("Legacy rule application is deprecated, use new configuration format");
    int count = 0;

    // Populate monitored filenames map
    if (count > 0) {
        int filename_count = ebpf_populate_filenames_map();
        FIM_LOG_INFO("✓ Applied %d rules, monitoring %d filenames", count, filename_count);
    }

    return count;
}

/* ========== Filename Monitoring ========== */

int ebpf_populate_filenames_map(void) {
    if (monitored_filenames_fd < 0) {
        FIM_LOG_ERROR("monitored_filenames_map not available");
        return FIM_ERROR_EBPF;
    }

    // Legacy filename population is no longer supported
    // Filenames are now populated through new configuration format
    int filename_count = 0;

    return filename_count;
}

int ebpf_add_monitored_filename(const char *filename) {
    if (!filename || monitored_filenames_fd < 0) {
        return FIM_ERROR_INVALID_PARAM;
    }

    char filename_key[64];
    safe_strncpy(filename_key, filename, sizeof(filename_key));

    __u32 flag = 1;
    int ret = bpf_map_update_elem(monitored_filenames_fd, filename_key, &flag, BPF_ANY);

    return ret == 0 ? FIM_SUCCESS : FIM_ERROR_EBPF;
}

/* ========== New Configuration Rule Application ========== */

/**
 * Apply new configuration format rules to eBPF maps
 * @param config: New configuration structure
 * @return: Number of rules applied, or negative error code
 */
int ebpf_apply_new_config(struct fim_new_config *config) {
    if (!config) {
        FIM_LOG_ERROR("Configuration is NULL");
        return FIM_ERROR_INVALID_PARAM;
    }

    if (!ebpf_is_loaded()) {
        FIM_LOG_ERROR("eBPF program not loaded");
        return FIM_ERROR_EBPF;
    }

    // Clear existing new configuration maps
    ebpf_clear_new_config_maps();

    int total_rules = 0;

    // Apply file_access configurations
    int file_access_rules = ebpf_apply_file_access_config(config->file_access_list);
    if (file_access_rules < 0) {
        FIM_LOG_ERROR("Failed to apply file_access configuration");
        return file_access_rules;
    }
    total_rules += file_access_rules;

    // Apply filesystem_protect configuration
    int fs_protect_result = ebpf_apply_filesystem_protect_config(config->filesystem_protect);
    if (fs_protect_result < 0) {
        FIM_LOG_ERROR("Failed to apply filesystem_protect configuration");
        return fs_protect_result;
    }

    // Populate monitored filenames map for inode update triggers
    int filename_count = ebpf_populate_new_filenames_map(config);
    if (filename_count < 0) {
        FIM_LOG_ERROR("Failed to populate monitored filenames");
        return filename_count;
    }

    FIM_LOG_INFO("✓ Applied new configuration: %d file_access rules, filesystem_protect=%s, monitoring %d filenames",
                file_access_rules,
                (config->filesystem_protect && config->filesystem_protect->enabled) ? "enabled" : "disabled",
                filename_count);

    return total_rules;
}

/**
 * Apply file_access configuration to eBPF maps
 * @param config_list: List of file_access configurations
 * @return: Number of rules applied, or negative error code
 */
int ebpf_apply_file_access_config(struct file_access_config *config_list) {


    if (file_access_map_fd < 0 || file_access_exceptions_fd < 0) {
        FIM_LOG_ERROR("file_access maps not available (file_access_map_fd=%d, file_access_exceptions_fd=%d)",
                      file_access_map_fd, file_access_exceptions_fd);
        return FIM_ERROR_EBPF;
    }

    int rule_count = 0;
    struct file_access_config *config = config_list;

    while (config) {
        if (!config->enabled) {
            config = config->next;
            continue;
        }

        FIM_LOG_INFO("Applying file_access config: rule_id=%s",
                    config->rule_id ? config->rule_id : "default");

        struct file_access_rule *rule = config->rules;
        while (rule) {
            // Get file device and inode
            __u32 file_dev, file_inode;
            if (get_path_dev_inode(rule->path, &file_dev, &file_inode) != 0) {
                file_dev = 0xFFFFFFFF;
                file_inode = 0xFFFFFFFF;
            }

            // Create file_access_key
            struct file_access_key key = {
                .dev = file_dev,
                .inode = file_inode,
                .access_type = rule->access_type
            };

            // Create file_access_value
            struct file_access_value value = {
                .rule_enabled = 1,
                .action = rule->action,
                .has_exceptions = (rule->allowed_list != NULL)
            };

            // Copy rule_id
            if (rule->sub_rule_id) {
                safe_strncpy(value.rule_id, rule->sub_rule_id, sizeof(value.rule_id));
            } else if (config->rule_id) {
                safe_strncpy(value.rule_id, config->rule_id, sizeof(value.rule_id));
            } else {
                strcpy(value.rule_id, "default");
            }

            // Add rule to map
            if (bpf_map_update_elem(file_access_map_fd, &key, &value, BPF_ANY) != 0) {
                FIM_LOG_ERROR("Failed to add file_access rule: %s", rule->path);
                return FIM_ERROR_EBPF;
            }

            // Legacy path-inode mapping removed

            FIM_LOG_INFO("  ✓ Added file_access rule: %s (dev: %u, inode: %u, %s, %s)",
                        rule->path, file_dev, file_inode,
                        rule->access_type == ACCESS_READ ? "read" : "write",
                        rule->action == ACTION_BLOCK ? "block" : "audit");

            // Add exceptions
            int exception_count = ebpf_add_file_access_exceptions(&key, rule->allowed_list);
            if (exception_count < 0) {
                FIM_LOG_ERROR("Failed to add exceptions for rule: %s", rule->path);
                return FIM_ERROR_EBPF;
            }



            rule_count++;
            rule = rule->next;
        }

        config = config->next;
    }

    return rule_count;
}

/**
 * Add file_access exceptions to eBPF map
 * @param rule_key: File access rule key
 * @param allowed_list: List of allowed entries
 * @return: Number of exceptions added, or negative error code
 */
int ebpf_add_file_access_exceptions(struct file_access_key *rule_key, struct allowed_entry *allowed_list) {
    if (!rule_key || !allowed_list) {
        return 0; // No exceptions to add
    }

    int exception_count = 0;
    struct allowed_entry *allowed = allowed_list;

    while (allowed) {
        __u32 process_dev = DEVICE_ANY, process_inode = 0; // Default wildcard (DEVICE_ANY, PROCESS_ANY)

        // Get process device and inode if specified
        if (allowed->process_path && strcmp(allowed->process_path, "*") != 0) {
            if (get_path_dev_inode(allowed->process_path, &process_dev, &process_inode) != 0) {
                process_dev = DEVICE_ANY; // DEVICE_ANY
                process_inode = 0;        // PROCESS_ANY
            }
        }

        // Create exception key
        struct file_access_exception_key ex_key = {
            .dev = rule_key->dev,
            .inode = rule_key->inode,
            .access_type = rule_key->access_type,
            .process_dev = process_dev,
            .process_inode = process_inode,
            .uid = allowed->uid
        };

        // Create exception value
        struct file_access_exception_value ex_value = {
            .allowed = 1
        };

        // Add exception to map
        if (bpf_map_update_elem(file_access_exceptions_fd, &ex_key, &ex_value, BPF_ANY) != 0) {
            FIM_LOG_ERROR("Failed to add file_access exception");
            return FIM_ERROR_EBPF;
        }



        exception_count++;
        allowed = allowed->next;
    }

    return exception_count;
}

/**
 * Apply filesystem_protect configuration to eBPF maps
 * @param config: Filesystem protect configuration
 * @return: 0 on success, negative error code on failure
 */
int ebpf_apply_filesystem_protect_config(struct filesystem_protect_config *config) {
    if (filesystem_protect_config_fd < 0) {
        FIM_LOG_ERROR("filesystem_protect_config map not available");
        return FIM_ERROR_EBPF;
    }

    if (!config) {
        // Disable filesystem protection
        __u32 config_key = 0;
        // Create eBPF-compatible structure
        struct {
            __u32 enabled;
            __u32 default_action;
            char rule_id[64];
        } disabled_config = {
            .enabled = 0,
            .default_action = ACTION_AUDIT
        };
        strcpy(disabled_config.rule_id, "disabled");

        if (bpf_map_update_elem(filesystem_protect_config_fd, &config_key, &disabled_config, BPF_ANY) != 0) {
            FIM_LOG_ERROR("Failed to disable filesystem_protect");
            return FIM_ERROR_EBPF;
        }

        FIM_LOG_INFO("✓ Filesystem protect disabled");
        return 0;
    }

    // Set filesystem protect configuration
    __u32 config_key = 0;
    // Create eBPF-compatible structure
    struct {
        __u32 enabled;
        __u32 default_action;
        char rule_id[64];
    } fs_config = {
        .enabled = config->enabled,
        .default_action = config->action
    };

    if (config->rule_id) {
        safe_strncpy(fs_config.rule_id, config->rule_id, sizeof(fs_config.rule_id));
    } else {
        strcpy(fs_config.rule_id, "default");
    }

    if (bpf_map_update_elem(filesystem_protect_config_fd, &config_key, &fs_config, BPF_ANY) != 0) {
        FIM_LOG_ERROR("Failed to set filesystem_protect config");
        return FIM_ERROR_EBPF;
    }

    FIM_LOG_INFO("✓ Filesystem protect: enabled=%s, action=%s, rule_id=%s",
                config->enabled ? "true" : "false",
                config->action == ACTION_BLOCK ? "block" : "audit",
                fs_config.rule_id);

    // Add exceptions if enabled
    if (config->enabled && config->allowed_list) {
        int exception_count = ebpf_add_filesystem_protect_exceptions(config->allowed_list);
        if (exception_count < 0) {
            FIM_LOG_ERROR("Failed to add filesystem_protect exceptions");
            return exception_count;
        }
        FIM_LOG_INFO("  Added %d filesystem_protect exceptions", exception_count);
    }

    return 0;
}

/**
 * Add filesystem_protect exceptions to eBPF map
 * @param allowed_list: List of filesystem allowed entries
 * @return: Number of exceptions added, or negative error code
 */
int ebpf_add_filesystem_protect_exceptions(struct filesystem_allowed_entry *allowed_list) {
    if (!allowed_list || filesystem_exceptions_fd < 0) {
        return 0;
    }

    int exception_count = 0;
    struct filesystem_allowed_entry *allowed = allowed_list;

    while (allowed) {
        __u32 file_dev = 0, file_inode = 0;
        __u32 process_dev = DEVICE_ANY, process_inode = 0; // Default wildcard (DEVICE_ANY, PROCESS_ANY)

        // Get file/directory device and inode
        if (allowed->path) {
            if (get_path_dev_inode(allowed->path, &file_dev, &file_inode) != 0) {
                file_dev = 0xFFFFFFFF;
                file_inode = 0xFFFFFFFF;
            }
        }

        // Get process device and inode if specified
        if (allowed->process_path && strcmp(allowed->process_path, "*") != 0) {
            if (get_path_dev_inode(allowed->process_path, &process_dev, &process_inode) != 0) {
                process_dev = DEVICE_ANY; // DEVICE_ANY
                process_inode = 0;        // PROCESS_ANY
            }
        }

        // Create exception key
        struct filesystem_exception_key ex_key = {
            .dev = file_dev,
            .inode = file_inode,
            .process_dev = process_dev,
            .process_inode = process_inode,
            .uid = allowed->uid
        };

        // Create exception value
        struct filesystem_exception_value ex_value = {
            .allowed = 1,
            .is_dir = allowed->is_dir
        };

        // Add exception to map
        if (bpf_map_update_elem(filesystem_exceptions_fd, &ex_key, &ex_value, BPF_ANY) != 0) {
            FIM_LOG_ERROR("Failed to add filesystem_protect exception");
            return FIM_ERROR_EBPF;
        }

        FIM_LOG_INFO("  ✓ Added filesystem exception: path=%s, process_dev=%u, process_inode=%u, uid=%u",
                    allowed->path ? allowed->path : "*", process_dev, process_inode, allowed->uid);

        exception_count++;
        allowed = allowed->next;
    }

    return exception_count;
}

/**
 * Populate monitored filenames map from new configuration
 * @param config: New configuration structure
 * @return: Number of filenames added, or negative error code
 */
int ebpf_populate_new_filenames_map(struct fim_new_config *config) {
    if (!config || monitored_filenames_fd < 0) {
        return 0;
    }

    int filename_count = 0;

    // Add filenames from file_access rules
    struct file_access_config *fa_config = config->file_access_list;
    while (fa_config) {
        if (!fa_config->enabled) {
            fa_config = fa_config->next;
            continue;
        }

        struct file_access_rule *rule = fa_config->rules;
        while (rule) {
            const char *filename = extract_filename(rule->path);
            if (strlen(filename) > 0 && strlen(filename) < 64) {
                char filename_key[64];
                safe_strncpy(filename_key, filename, sizeof(filename_key));

                __u32 flag = 1;
                if (bpf_map_update_elem(monitored_filenames_fd, filename_key, &flag, BPF_ANY) == 0) {
                    filename_count++;
                } else {
                    FIM_LOG_ERROR("❌ Failed to add filename: %s", filename_key);
                }
            }
            rule = rule->next;
        }
        fa_config = fa_config->next;
    }

    return filename_count;
}

/**
 * Clear new configuration maps
 */
void ebpf_clear_new_config_maps(void) {

    // Clear file_access_map
    if (file_access_map_fd >= 0) {
        // Get the first key
        struct file_access_key key, next_key;
        int ret = bpf_map_get_next_key(file_access_map_fd, NULL, &key);

        while (ret == 0) {
            // Get next key before deleting current one
            int next_ret = bpf_map_get_next_key(file_access_map_fd, &key, &next_key);

            // Delete current key
            bpf_map_delete_elem(file_access_map_fd, &key);

            // Move to next key
            if (next_ret != 0) {
                break;
            }
            key = next_key;
            ret = 0;
        }

    }

    // Clear file_access_exc (exceptions map)
    if (file_access_exceptions_fd >= 0) {
        struct file_access_exception_key exc_key, exc_next_key;
        int ret = bpf_map_get_next_key(file_access_exceptions_fd, NULL, &exc_key);

        while (ret == 0) {
            int next_ret = bpf_map_get_next_key(file_access_exceptions_fd, &exc_key, &exc_next_key);

            bpf_map_delete_elem(file_access_exceptions_fd, &exc_key);

            if (next_ret != 0) {
                break;
            }
            exc_key = exc_next_key;
            ret = 0;
        }

    }

    // Clear filesystem_exce (filesystem exceptions map)
    if (filesystem_exceptions_fd >= 0) {
        struct filesystem_exception_key fs_key, fs_next_key;
        int ret = bpf_map_get_next_key(filesystem_exceptions_fd, NULL, &fs_key);

        while (ret == 0) {
            int next_ret = bpf_map_get_next_key(filesystem_exceptions_fd, &fs_key, &fs_next_key);

            bpf_map_delete_elem(filesystem_exceptions_fd, &fs_key);

            if (next_ret != 0) {
                break;
            }
            fs_key = fs_next_key;
            ret = 0;
        }

    }

    // Clear monitored_filenames_map
    if (monitored_filenames_fd >= 0) {
        char filename_key[64], filename_next_key[64];
        int ret = bpf_map_get_next_key(monitored_filenames_fd, NULL, filename_key);

        while (ret == 0) {
            int next_ret = bpf_map_get_next_key(monitored_filenames_fd, filename_key, filename_next_key);

            bpf_map_delete_elem(monitored_filenames_fd, filename_key);

            if (next_ret != 0) {
                break;
            }
            memcpy(filename_key, filename_next_key, sizeof(filename_key));
            ret = 0;
        }

    }


}
