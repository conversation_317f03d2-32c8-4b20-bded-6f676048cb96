file_access:
  - rule_id: "exception_test"
    enabled: true
    rules:
      # 默认阻止写入的文件，只有 allowed 中的进程/用户可以写入
      - path: "/home/<USER>/test/protected_file.txt"
        sub_rule_id: "protected_write"
        access: "write"
        dir: false
        action: "block"
        allowed:
          # 允许 bash 进程写入
          - process: "/bin/bash"
            uid: 1000
          # 允许 echo 命令写入
          - process: "/bin/echo"
            uid: 1000

      # 默认阻止读取的文件，只有 allowed 中的进程可以读取
      - path: "/home/<USER>/test/secret_file.txt"
        sub_rule_id: "secret_read"
        access: "read"
        dir: false
        action: "block"
        allowed:
          # 只允许 cat 命令读取
          - process: "/bin/cat"
            uid: 1000

      # 测试目录访问控制
      - path: "/home/<USER>/test/restricted_dir"
        sub_rule_id: "dir_access"
        access: "write"
        dir: true
        action: "block"
        allowed:
          # 只允许当前用户的任何进程访问
          - process: "*"
            uid: 1000

filesystem_protect:
  - rule_id: "global_protection"
    enabled: false
    action: "block"
