# FIM Installation Guide

This guide provides detailed instructions for installing and setting up the FIM (File Integrity Monitoring) system.

## Prerequisites

### Kernel Requirements

FIM requires a modern Linux kernel with eBPF and LSM support:

- **Linux kernel 5.8 or later**
- **eBPF support enabled** (`CONFIG_BPF=y`)
- **BPF LSM support enabled** (`CONFIG_BPF_LSM=y`)
- **LSM framework enabled** (`CONFIG_SECURITY=y`)

#### Verify Kernel Support

Check if your kernel supports the required features:

```bash
# Check kernel version
uname -r

# Check if BPF LSM is available
cat /sys/kernel/security/lsm | grep bpf

# Check if eBPF is supported
ls /sys/fs/bpf/
```

### System Dependencies

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install -y \
    build-essential \
    clang \
    llvm \
    libbpf-dev \
    libelf-dev \
    libyaml-dev \
    linux-headers-$(uname -r)
```

#### RHEL/CentOS/Fedora
```bash
sudo dnf install -y \
    gcc \
    clang \
    llvm \
    libbpf-devel \
    elfutils-libelf-devel \
    libyaml-devel \
    kernel-headers \
    kernel-devel
```

#### Arch Linux
```bash
sudo pacman -S \
    base-devel \
    clang \
    llvm \
    libbpf \
    libelf \
    libyaml \
    linux-headers
```

## Compilation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd FIM
```

### 2. Build the Project
```bash
make clean
make
```

The build process will:
- Compile eBPF programs using `clang`
- Build userspace components
- Create the `fim-ctl` binary in the `bin/` directory

### 3. Verify Build
```bash
./bin/fim-ctl --help
```

## Installation

### Option 1: Manual Installation

#### Install Binary
```bash
sudo cp bin/fim-ctl /usr/local/bin/
sudo chmod +x /usr/local/bin/fim-ctl
```

#### Create Configuration Directory
```bash
sudo mkdir -p /etc/fim
sudo cp config/policy.yaml /etc/fim/
```

#### Install Service (Optional)
```bash
sudo cp scripts/fim.service /etc/systemd/system/
sudo systemctl daemon-reload
```

### Option 2: Automated Installation

Use the provided installation script:

```bash
sudo ./scripts/install.sh
```

This script will:
- Install the binary to `/usr/local/bin/`
- Create configuration directory `/etc/fim/`
- Copy default configuration files
- Install systemd service file
- Set appropriate permissions

## Configuration Setup

### 1. Create Configuration File

Start with the basic example configuration:

```bash
sudo cp examples/configs/basic_example.yaml /etc/fim/policy.yaml
```

### 2. Edit Configuration

Edit the configuration file to match your requirements:

```bash
sudo nano /etc/fim/policy.yaml
```

### 3. Test Configuration

Validate your configuration before starting the service:

```bash
sudo fim-ctl test /etc/fim/policy.yaml
```

## Service Management

### Enable and Start Service
```bash
sudo systemctl enable fim
sudo systemctl start fim
```

### Check Service Status
```bash
sudo systemctl status fim
```

### View Logs
```bash
sudo journalctl -u fim -f
```

### Stop Service
```bash
sudo systemctl stop fim
```

## Verification

### 1. Test Basic Functionality

Create a test file and verify monitoring:

```bash
# Start FIM with test configuration
sudo fim-ctl start examples/configs/basic_example.yaml

# In another terminal, test file operations
echo "test" > /tmp/test_file.txt
cat /tmp/test_file.txt

# Check FIM logs for events
```

### 2. Verify eBPF Programs

Check if eBPF programs are loaded:

```bash
sudo bpftool prog list | grep fim
sudo bpftool map list | grep -E "(file_access|filesystem)"
```

## Troubleshooting

### Common Issues

#### 1. Permission Denied
```
Error: Failed to load eBPF program: Permission denied
```
**Solution**: Ensure you're running as root and have CAP_SYS_ADMIN capability.

#### 2. Kernel Not Supported
```
Error: BPF LSM not supported
```
**Solution**: Upgrade to a kernel with BPF LSM support (5.8+) or recompile kernel with `CONFIG_BPF_LSM=y`.

#### 3. Missing Dependencies
```
Error: libbpf not found
```
**Solution**: Install required development packages as listed in prerequisites.

#### 4. Compilation Errors
```
Error: clang not found
```
**Solution**: Install clang and LLVM development tools.

### Debug Mode

Enable debug logging for troubleshooting:

```bash
export FIM_DEBUG=1
sudo fim-ctl start /etc/fim/policy.yaml
```

### Log Files

Check system logs for detailed error messages:

```bash
sudo journalctl -u fim --since "1 hour ago"
sudo dmesg | grep -i bpf
```

## Uninstallation

To remove FIM from your system:

```bash
sudo ./scripts/uninstall.sh
```

Or manually:

```bash
sudo systemctl stop fim
sudo systemctl disable fim
sudo rm /etc/systemd/system/fim.service
sudo rm /usr/local/bin/fim-ctl
sudo rm -rf /etc/fim
sudo systemctl daemon-reload
```

## Security Considerations

- FIM requires root privileges to load eBPF programs
- Configuration files should be protected (readable only by root)
- Monitor FIM logs for potential security events
- Regularly update FIM and kernel for security patches

## Performance Tuning

### Ring Buffer Size

Adjust ring buffer sizes in the eBPF code if experiencing high event volumes:

```c
// In src/ebpf/fim_common.h
#define LOG_EVENTS_BUFFER_SIZE (2 * 1024 * 1024)  // 2MB
#define INODE_EVENTS_BUFFER_SIZE (512 * 1024)     // 512KB
```

### Map Sizes

Increase map sizes for environments with many monitored files:

```c
// In src/ebpf/fim_common.h
__uint(max_entries, 2048);  // Increase from 1024
```

Recompile after making changes:

```bash
make clean && make
```
