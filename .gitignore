# Build artifacts
build/
bin/
*.o
*.so
*.a

# Compiled eBPF programs
*.bpf.o
*.skel.h

# Editor files
*~
*.swp
*.swo
.vscode/
.idea/

# OS files
.DS_Store
Thumbs.db

# Log files
*.log
logs/

# Runtime files
*.pid
/var/run/fim.pid

# Temporary files
*.tmp
*.temp
.tmp/

# Debug files
core
*.core
*.dSYM/

# Package files
*.deb
*.rpm
*.tar.gz
*.zip

# Configuration backups
*.conf.bak
*.yaml.bak
*.yml.bak

# Test artifacts
test_results/
coverage/

# Documentation build
docs/_build/
docs/.doctrees/

# Python cache (if any Python scripts)
__pycache__/
*.pyc
*.pyo

# Local development
.env
.local/
local/
