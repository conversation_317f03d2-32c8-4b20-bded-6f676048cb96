#include "utils.h"

/* ========== String Utilities ========== */

const char* extract_filename(const char* path) {
    if (!path) return "";
    const char* filename = strrchr(path, '/');
    return filename ? filename + 1 : path;
}

int safe_strncpy(char *dest, const char *src, size_t dest_size) {
    if (!dest || !src || dest_size == 0) {
        return -1;
    }

    strncpy(dest, src, dest_size - 1);
    dest[dest_size - 1] = '\0';
    return 0;
}

int is_filename_match(const char* rule_path, const char* event_filename) {
    if (!rule_path || !event_filename) {
        return 0;
    }

    const char* rule_filename = extract_filename(rule_path);
    return strcmp(rule_filename, event_filename) == 0;
}

/* ========== File System Utilities ========== */

int get_path_inode(const char *path, __u32 *inode) {
    struct stat st;

    if (!path || !inode) {
        return -1;
    }

    if (stat(path, &st) != 0) {
        return -1;
    }

    *inode = (__u32)st.st_ino;
    return 0;
}

int get_path_dev_inode(const char *path, __u32 *dev, __u32 *inode) {
    struct stat st;

    if (!path || !dev || !inode) {
        return -1;
    }

    if (stat(path, &st) != 0) {
        return -1;
    }

    // Convert to kernel format: (major << 20) | minor
    // This matches the dev_t format used in kernel space
    unsigned int maj = major(st.st_dev);
    unsigned int min = minor(st.st_dev);
    *dev = (__u32)((maj << 20) | min);
    *inode = (__u32)st.st_ino;
    return 0;
}

int path_exists(const char *path) {
    struct stat st;
    return (path && stat(path, &st) == 0) ? 1 : 0;
}

/* ========== User/Process Utilities ========== */

const char *get_username_from_uid(__u32 uid) {
    static char username_buffer[256];
    struct passwd *pwd = getpwuid(uid);

    if (pwd && pwd->pw_name) {
        safe_strncpy(username_buffer, pwd->pw_name, sizeof(username_buffer));
        return username_buffer;
    }

    snprintf(username_buffer, sizeof(username_buffer), "%u", uid);
    return username_buffer;
}

__u32 get_uid_from_username(const char *username) {
    if (!username) {
        return UINT32_MAX;
    }

    struct passwd *pwd = getpwnam(username);
    return pwd ? pwd->pw_uid : UINT32_MAX;
}

/* ========== Operation/Action Name Utilities ========== */

const char *get_operation_name(__u32 operation) {
    switch (operation) {
        case FIM_OP_READ: return "READ";
        case FIM_OP_WRITE: return "WRITE";
        case FIM_OP_REMOVE: return "REMOVE";
        default: return "UNKNOWN";
    }
}

const char *get_action_name(__u32 action) {
    switch (action) {
        case ACTION_BLOCK: return "BLOCK";
        case ACTION_AUDIT: return "AUDIT";
        default: return "UNKNOWN";
    }
}

/* get_result_name function removed - no longer needed */

/* ========== Legacy Rule Management (Removed) ========== */
// Legacy rule management functions have been removed
// Rules are now managed through new configuration format

/* ========== Time Utilities ========== */

void format_timestamp(__u64 timestamp, char *buffer, size_t buffer_size) {
    if (!buffer || buffer_size == 0) {
        return;
    }

    time_t sec = timestamp / 1000000000ULL;
    struct tm *tm_info = localtime(&sec);

    if (tm_info) {
        strftime(buffer, buffer_size, "%Y-%m-%d %H:%M:%S", tm_info);
    } else {
        snprintf(buffer, buffer_size, "Invalid timestamp");
    }
}
