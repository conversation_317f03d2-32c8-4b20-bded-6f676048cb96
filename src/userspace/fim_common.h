#ifndef FIM_COMMON_H
#define FIM_COMMON_H

/* System headers */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <signal.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/sysmacros.h>
#include <pwd.h>
#include <grp.h>
#include <time.h>
#include <pthread.h>

/* External libraries */
#include <bpf/libbpf.h>
#include <bpf/bpf.h>
#include <yaml.h>

/* Local headers */
#include "../include/fim_shared.h"

/* ========== Constants ========== */
#define FIM_VERSION_STRING "2.0.0"
#define MAX_PATH_LEN 256
#define MAX_FILENAME_LEN 64
#define MAX_COMM_LEN 16
#define MAX_LINKS 32
#define MAX_RULES 1024
#define DEFAULT_CONFIG_FILE "config/policy.yaml"

/* Event processing constants */
#define RING_BUFFER_POLL_TIMEOUT_MS 100
#define EVENT_LOOP_SLEEP_US 10000

/* Operation types (compatible with eBPF code) */
#define FIM_OP_READ   1
#define FIM_OP_WRITE  2
#define FIM_OP_REMOVE 3

/* Access types */
#define ACCESS_READ  1
#define ACCESS_WRITE 2

/* Action types */
#define ACTION_AUDIT 1  /* Audit mode: log but allow */
#define ACTION_BLOCK 2  /* Block mode: deny operation */

/* Special values */
#define UID_ANY     UINT32_MAX
#define DEVICE_ANY  UINT32_MAX  /* Device wildcard - use max value to avoid conflicts */
#define PROCESS_ANY "*"

/* ========== Error Codes ========== */
typedef enum {
    FIM_SUCCESS = 0,
    FIM_ERROR_CONFIG = -1,
    FIM_ERROR_EBPF = -2,
    FIM_ERROR_PERMISSION = -3,
    FIM_ERROR_MEMORY = -4,
    FIM_ERROR_IO = -5,
    FIM_ERROR_INVALID_PARAM = -6
} fim_error_t;

/* ========== eBPF Data Structures ========== */

/* Log event structure - timestamp added in userspace */
struct fim_log_event {
    __u32 pid;              /* Process ID */
    __u32 uid;              /* User ID */
    __u32 operation;        /* Operation type (FIM_OP_*) */
    __u32 action;           /* Rule action (ACTION_*) */
    __u32 dev;              /* File device number (major:minor) */
    __u32 inode;            /* File inode */
    char comm[16];          /* Process name */
    char target_path[256];  /* Target file path */
};

/* Simple file event structure for inode changes */
struct simple_file_event {
    __u64 timestamp;        /* Event timestamp */
    __u32 event_type;       /* Event type (CREATE/DELETE/RENAME) */
    __u32 pid;              /* Process ID */
    char filename[64];      /* Filename only */
};

/* ========== Configuration Structures ========== */

/* Allowed entry for exceptions */
struct allowed_entry {
    char *process_path;     /* Process path, NULL or "*" means any */
    __u32 uid;              /* User ID, UINT32_MAX means any */
    struct allowed_entry *next;
};

/* File access rule */
struct file_access_rule {
    char *path;             /* File path */
    char *sub_rule_id;      /* Sub rule ID */
    int access_type;        /* ACCESS_READ or ACCESS_WRITE */
    int is_dir;             /* Is directory flag */
    int action;             /* ACTION_AUDIT or ACTION_BLOCK */
    struct allowed_entry *allowed_list;  /* Exception list */
    struct file_access_rule *next;
};

/* File access configuration */
struct file_access_config {
    int enabled;            /* Rule group enabled */
    char *rule_id;          /* Rule group ID */
    struct file_access_rule *rules;  /* Rule list */
    struct file_access_config *next;
};

/* Filesystem protect allowed entry */
struct filesystem_allowed_entry {
    char *path;             /* Path */
    int is_dir;             /* Is directory flag */
    char *process_path;     /* Process path, NULL or "*" means any */
    __u32 uid;              /* User ID, UINT32_MAX means any */
    struct filesystem_allowed_entry *next;
};

/* Filesystem protect configuration */
struct filesystem_protect_config {
    int enabled;            /* Protection enabled */
    char *rule_id;          /* Rule ID */
    int action;             /* Default action */
    struct filesystem_allowed_entry *allowed_list;  /* Exception list */
};

/* Main configuration structure */
struct fim_new_config {
    struct file_access_config *file_access_list;
    struct filesystem_protect_config *filesystem_protect;
};

/* ========== Global State ========== */
extern volatile sig_atomic_t running;
extern volatile sig_atomic_t stop_requested;
extern char *current_config_file;
extern struct fim_new_config *current_fim_config;

/* ========== eBPF Resources ========== */
extern struct bpf_object *bpf_obj;
extern struct bpf_link *links[MAX_LINKS];
extern int link_count;

/* Map file descriptors */
extern int monitored_filenames_fd;
extern int inode_events_fd;

/* ========== Utility Macros ========== */
#define FIM_CHECK(expr, error_code, msg) \
    do { \
        if (!(expr)) { \
            fprintf(stderr, "Error: %s\n", msg); \
            return error_code; \
        } \
    } while(0)

#define FIM_CHECK_NULL(ptr, msg) \
    FIM_CHECK((ptr) != NULL, FIM_ERROR_MEMORY, msg)

/* ========== Logging Macros ========== */
#ifdef FIM_LOG_ERROR
#undef FIM_LOG_ERROR
#endif
#ifdef FIM_LOG_INFO
#undef FIM_LOG_INFO
#endif

#define FIM_LOG_ERROR(fmt, ...) \
    fprintf(stderr, "[ERROR] " fmt "\n", ##__VA_ARGS__)

#define FIM_LOG_INFO(fmt, ...) \
    printf("[INFO] " fmt "\n", ##__VA_ARGS__)

/* ========== Function Declarations ========== */

/* Signal handling */
void signal_handler(int sig);
void setup_signal_handlers(void);

/* System utilities */
int check_root_privileges(void);
void cleanup_resources(void);

/* ========== eBPF Map Structures ========== */

/* File access rule key - now includes device number for unique identification */
struct file_access_key {
    __u32 dev;         /* Device number (major:minor) */
    __u32 inode;       /* File inode */
    __u32 access_type; /* ACCESS_READ or ACCESS_WRITE */
};

/* File access rule value */
struct file_access_value {
    __u32 rule_enabled;   /* Rule enabled flag */
    __u32 action;         /* ACTION_AUDIT or ACTION_BLOCK */
    __u32 has_exceptions; /* Has exception rules flag */
    char rule_id[64];     /* Rule ID for logging */
};

/* File access exception key - now includes device numbers for unique identification */
struct file_access_exception_key {
    __u32 dev;           /* File device number (major:minor) */
    __u32 inode;         /* File inode */
    __u32 access_type;   /* Access type */
    __u32 process_dev;   /* Process executable device number (0 = wildcard) */
    __u32 process_inode; /* Process executable inode (0 = wildcard) */
    __u32 uid;           /* User ID (UID_ANY = wildcard) */
};

/* File access exception value */
struct file_access_exception_value {
    __u32 allowed; /* Allow flag */
};

/* Filesystem protect exception key - now includes device numbers for unique identification */
struct filesystem_exception_key {
    __u32 dev;           /* File/directory device number (major:minor) */
    __u32 inode;         /* File/directory inode */
    __u32 process_dev;   /* Process executable device number (0 = wildcard) */
    __u32 process_inode; /* Process executable inode (0 = wildcard) */
    __u32 uid;           /* User ID (UID_ANY = wildcard) */
};

/* Filesystem protect exception value */
struct filesystem_exception_value {
    __u32 allowed; /* Allow write flag */
    __u32 is_dir;  /* Directory flag */
};

#endif /* FIM_COMMON_H */
