#ifndef __FIM_SHARED_H__
#define __FIM_SHARED_H__

#include <linux/types.h>

#define FIM_MAX_PATH_LEN    4096
#define FIM_MAX_FILENAME    256
#define FIM_MAX_WHITELIST   10000

enum fim_operation {
    FIM_OP_OPEN_READ    = 1,
    FIM_OP_OPEN_WRITE   = 2,
    FIM_OP_CREATE       = 3,
    FIM_OP_DELETE       = 4,
    FIM_OP_RENAME       = 5,
    FIM_OP_CHMOD        = 6,
    FIM_OP_CHOWN        = 7,
    FIM_OP_TRUNCATE     = 8,
};

enum fim_decision {
    FIM_ALLOW   = 0,
    FIM_DENY    = 1,
};

struct fim_inode_key {
    __u64 ino;
    __u32 dev;
};

struct fim_policy_entry {
    __u32 operations;
    __u32 action;
    char description[64];
};

struct fim_event {
    __u64 timestamp;
    __u32 pid;
    __u32 uid;
    __u32 operation;
    __u32 decision;
    __u64 ino;
    __u32 dev;
    char path[FIM_MAX_PATH_LEN];
    char comm[16];
};

struct fim_stats {
    __u64 total_events;
    __u64 allowed_events;
    __u64 denied_events;
    __u64 policy_hits;
};

#define FIM_TYPE_FILE       0x01
#define FIM_TYPE_DIR        0x02

#define FIM_PERM_READ       0x01
#define FIM_PERM_WRITE      0x02
#define FIM_PERM_CREATE     0x04
#define FIM_PERM_DELETE     0x08
#define FIM_PERM_RENAME     0x10
#define FIM_PERM_CHMOD      0x20
#define FIM_PERM_CHOWN      0x40
#define FIM_PERM_ALL        0xFF

#define FIM_MAP_WHITELIST   0
#define FIM_MAP_EVENTS      1
#define FIM_MAP_STATS       2
#define FIM_MAP_CONFIG      3

struct fim_config {
    __u32 enabled;
    __u32 log_level;
    __u32 default_action;
    __u32 max_events;
};

#define FIM_POLICY_ALLOW    0
#define FIM_POLICY_DENY     1

#define FIM_RULE_ALLOW      0
#define FIM_RULE_DENY       1

#define FIM_LOG_ERROR   1
#define FIM_LOG_WARN    2
#define FIM_LOG_INFO    3
#define FIM_LOG_DEBUG   4

enum fim_cmd {
    FIM_CMD_ADD_POLICY      = 1,
    FIM_CMD_DEL_POLICY      = 2,
    FIM_CMD_CLEAR_POLICY    = 3,
    FIM_CMD_GET_STATS       = 4,
    FIM_CMD_SET_CONFIG      = 5,
    FIM_CMD_GET_CONFIG      = 6,
};

struct fim_ctrl_msg {
    __u32 cmd;
    __u32 data_len;
    union {
        struct fim_policy_entry policy;
        struct fim_stats stats;
        struct fim_config config;
    };
};

#endif
