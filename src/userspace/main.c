/*
 * File Integrity Monitoring (FIM) - Main Program
 *
 * This is the main entry point for the FIM system.
 * It handles command line parsing and coordinates between different modules.
 */

#include "fim_common.h"
#include "config.h"
#include "ebpf_manager.h"
#include "event_handler.h"
#include "utils.h"

/* Constants */
#define EBPF_OBJECT_FILE "build/fim_lsm.o"

/* Function declarations */
int start_fim_new(const char *config_file);

/* ========== Global Variables ========== */
volatile sig_atomic_t running = 1;
volatile sig_atomic_t stop_requested = 0;
char *current_config_file = NULL;
struct fim_new_config *current_fim_config = NULL;

/* eBPF Resources */
struct bpf_object *bpf_obj = NULL;
struct bpf_link *links[MAX_LINKS];
int link_count = 0;

/* Map file descriptors */
int monitored_filenames_fd = -1;
int inode_events_fd = -1;

/* ========== Signal Handling ========== */

void signal_handler(int sig) {
    switch (sig) {
        case SIGINT:
        case SIGTERM:
            FIM_LOG_INFO("Received signal %d, shutting down...", sig);
            running = 0;
            stop_requested = 1;
            break;
        case SIGHUP:
            FIM_LOG_INFO("Received SIGHUP, configuration reload not supported");
            break;
        default:
            break;
    }
}

void setup_signal_handlers(void) {
    struct sigaction sa;
    sa.sa_handler = signal_handler;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = 0;

    sigaction(SIGINT, &sa, NULL);
    sigaction(SIGTERM, &sa, NULL);
    sigaction(SIGHUP, &sa, NULL);
}

/* ========== System Utilities ========== */

int check_root_privileges(void) {
    if (geteuid() != 0) {
        FIM_LOG_ERROR("This program requires root privileges");
        FIM_LOG_ERROR("Please run with sudo or as root user");
        return FIM_ERROR_PERMISSION;
    }
    return FIM_SUCCESS;
}

void cleanup_resources(void) {
    FIM_LOG_INFO("Cleaning up resources...");

    event_handler_cleanup();
    ebpf_cleanup();
    clear_config();

    FIM_LOG_INFO("Cleanup completed");
}

/* ========== Command Handlers ========== */

static int handle_start_command(int argc, char *argv[], int optind) {
    const char *config_file = DEFAULT_CONFIG_FILE;

    // Check for config file argument
    if (optind < argc) {
        config_file = argv[optind];
    }

    // Use new configuration format
    return start_fim_new(config_file);
}

static int handle_test_command(int argc, char *argv[], int optind) {
    const char *config_file = DEFAULT_CONFIG_FILE;

    if (optind < argc) {
        config_file = argv[optind];
    }

    printf("Testing YAML configuration format...\n");

    int result = parse_new_yaml_config(config_file);
    if (result == FIM_SUCCESS) {
        printf("✓ Configuration test passed\n");
    } else {
        printf("✗ Configuration test failed\n");
    }

    return result;
}



/**
 * Start FIM with new configuration format
 * @param config_file: Path to new format configuration file
 * @return: 0 on success, negative error code on failure
 */
int start_fim_new(const char *config_file) {
    int result = FIM_SUCCESS;

    FIM_LOG_INFO("Starting FIM with new configuration format: %s", config_file);

    // Parse new configuration
    result = parse_new_yaml_config(config_file);
    if (result != FIM_SUCCESS) {
        FIM_LOG_ERROR("Failed to parse new configuration file");
        return result;
    }

    if (!current_fim_config) {
        FIM_LOG_ERROR("No configuration loaded");
        return FIM_ERROR_CONFIG;
    }

    // Load eBPF program
    result = ebpf_load_program(EBPF_OBJECT_FILE);
    if (result != FIM_SUCCESS) {
        FIM_LOG_ERROR("Failed to load eBPF program");
        goto cleanup;
    }

    // Apply new configuration to eBPF maps
    result = ebpf_apply_new_config(current_fim_config);
    if (result < 0) {
        FIM_LOG_ERROR("Failed to apply new configuration to eBPF maps");
        goto cleanup;
    }

    FIM_LOG_INFO("Applied %d rules from new configuration", result);

    // Attach eBPF programs
    result = ebpf_attach_programs();
    if (result != FIM_SUCCESS) {
        FIM_LOG_ERROR("Failed to attach eBPF programs");
        goto cleanup;
    }

    // Initialize event handler after eBPF programs are loaded and attached
    result = event_handler_init();
    if (result != FIM_SUCCESS) {
        FIM_LOG_ERROR("Failed to initialize event handler");
        goto cleanup;
    }

    // Start event handler
    result = event_handler_start();
    if (result != FIM_SUCCESS) {
        FIM_LOG_ERROR("Failed to start event handler");
        goto cleanup;
    }

    FIM_LOG_INFO("FIM started successfully with new configuration format");
    FIM_LOG_INFO("Press Ctrl+C to stop...");

    // Main loop
    while (running && !stop_requested) {
        sleep(1);
    }

    FIM_LOG_INFO("Shutting down FIM...");

cleanup:
    // Cleanup
    event_handler_stop();
    event_handler_cleanup();
    // Note: ebpf_detach_programs and ebpf_unload_program don't exist yet
    // Using ebpf_cleanup instead
    ebpf_cleanup();

    if (current_fim_config) {
        free_new_config(current_fim_config);
        current_fim_config = NULL;
    }

    FIM_LOG_INFO("FIM shutdown complete");
    return result;
}

static void print_usage(const char *prog_name) {
    printf("FIM (File Integrity Monitoring) Control Tool v%s\n\n", FIM_VERSION_STRING);
    printf("Usage: %s COMMAND [config_file]\n\n", prog_name);
    printf("COMMANDS:\n");
    printf("  start [config_file]     Start FIM protection\n");
    printf("  test [config_file]      Test configuration file\n");
    printf("  help                    Show this help\n");
    printf("  version                 Show version information\n\n");
    printf("OPTIONS:\n");
    printf("  -h                     Show help\n\n");
    printf("EXAMPLES:\n");
    printf("  %s start                           # Start with default config\n", prog_name);
    printf("  %s start config/custom.yaml        # Start with custom config\n", prog_name);
    printf("  %s test config/test.yaml           # Test configuration\n", prog_name);
    printf("\nFor more information, see the documentation.\n");
}

static void print_version(void) {
    printf("FIM (File Integrity Monitoring) v%s\n", FIM_VERSION_STRING);
    printf("Built with eBPF LSM support\n");
    printf("Copyright (c) 2024\n");
}

/* ========== Main Function ========== */

int main(int argc, char *argv[]) {
    int opt;

    // Parse command line options
    while ((opt = getopt(argc, argv, "h")) != -1) {
        switch (opt) {
            case 'h':
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }

    // Check for command
    if (optind >= argc) {
        FIM_LOG_ERROR("No command specified");
        print_usage(argv[0]);
        return 1;
    }

    const char *command = argv[optind++];

    // Check root privileges for most commands
    if (strcmp(command, "help") != 0 && strcmp(command, "version") != 0) {
        int result = check_root_privileges();
        if (result != FIM_SUCCESS) {
            return 1;
        }
    }

    // Setup signal handlers
    setup_signal_handlers();

    // Handle commands
    int result = FIM_SUCCESS;

    if (strcmp(command, "start") == 0) {
        result = handle_start_command(argc, argv, optind);
    } else if (strcmp(command, "test") == 0) {
        result = handle_test_command(argc, argv, optind);
    } else if (strcmp(command, "help") == 0) {
        print_usage(argv[0]);
    } else if (strcmp(command, "version") == 0) {
        print_version();
    } else {
        FIM_LOG_ERROR("Unknown command: %s", command);
        print_usage(argv[0]);
        result = FIM_ERROR_INVALID_PARAM;
    }

    // Cleanup
    cleanup_resources();

    return result == FIM_SUCCESS ? 0 : 1;
}
