# FIM Configuration Reference

This document provides a comprehensive reference for configuring the FIM (File Integrity Monitoring) system using YAML configuration files.

## Configuration File Format

FIM uses YAML configuration files with two main sections:
- `file_access` - Specific file and directory access rules
- `filesystem_protect` - Global filesystem protection settings

## File Access Rules

The `file_access` section defines specific rules for monitoring and controlling access to files and directories.

### Basic Structure

```yaml
file_access:
  - rule_id: "unique_rule_identifier"
    enabled: true|false
    rules:
      - path: "/path/to/file/or/directory"
        sub_rule_id: "sub_rule_identifier"
        access: "read"|"write"
        dir: true|false
        action: "block"|"audit"
        allowed:
          - process: "/path/to/process"|"*"
            uid: user_id|4294967295
```

### Parameters

#### Rule Group Parameters
- **`rule_id`** (string, required): Unique identifier for the rule group
- **`enabled`** (boolean, required): Whether this rule group is active

#### Individual Rule Parameters
- **`path`** (string, required): Absolute path to file or directory to monitor
- **`sub_rule_id`** (string, optional): Unique identifier for this specific rule
- **`access`** (string, required): Type of access to control
  - `"read"` - Monitor/control read operations
  - `"write"` - Monitor/control write operations (includes create, modify, delete)
- **`dir`** (boolean, optional): Whether the path is a directory (default: false)
- **`action`** (string, required): Action to take when rule matches
  - `"block"` - Deny access and log the event
  - `"audit"` - Allow access but log the event

#### Exception Parameters (`allowed` section)
- **`process`** (string, required): Process executable path or wildcard
  - Absolute path: `/usr/bin/vim`
  - Wildcard: `"*"` (any process)
- **`uid`** (integer, required): User ID or wildcard
  - Specific UID: `1000`
  - Wildcard: `4294967295` (any user)

### File Access Examples

#### Protect System Configuration
```yaml
file_access:
  - rule_id: "system_config_protection"
    enabled: true
    rules:
      - path: "/etc/passwd"
        access: "write"
        action: "block"
        allowed:
          - process: "/usr/bin/passwd"
            uid: 0
          - process: "/usr/sbin/useradd"
            uid: 0
```

#### Monitor Application Directory
```yaml
file_access:
  - rule_id: "app_monitoring"
    enabled: true
    rules:
      - path: "/opt/myapp"
        access: "write"
        action: "audit"
        dir: true
        allowed:
          - process: "*"
            uid: 1000
```

#### Read-Only File Protection
```yaml
file_access:
  - rule_id: "readonly_protection"
    enabled: true
    rules:
      - path: "/etc/myapp/config.conf"
        access: "read"
        action: "audit"
      - path: "/etc/myapp/config.conf"
        access: "write"
        action: "block"
        allowed:
          - process: "/usr/bin/myapp"
            uid: 1000
```

## Filesystem Protection

The `filesystem_protect` section provides global filesystem protection with a default-deny approach.

### Basic Structure

```yaml
filesystem_protect:
  - rule_id: "global_protection_rule"
    enabled: true|false
    action: "block"|"audit"
    allowed:
      - path: "/path/to/allowed/location"
        process_path: "/path/to/process"|"*"
        uid: user_id|4294967295
        is_dir: true|false
```

### Parameters

- **`rule_id`** (string, required): Unique identifier for the protection rule
- **`enabled`** (boolean, required): Whether global protection is active
- **`action`** (string, required): Default action for non-allowed operations
  - `"block"` - Deny all write operations by default
  - `"audit"` - Log all write operations by default

#### Exception Parameters (`allowed` section)
- **`path`** (string, required): Path to allow write access
- **`process_path`** (string, required): Process executable path or wildcard
- **`uid`** (integer, required): User ID or wildcard
- **`is_dir`** (boolean, optional): Whether the path is a directory

### Filesystem Protection Examples

#### Global Read-Only with Exceptions
```yaml
filesystem_protect:
  - rule_id: "global_readonly"
    enabled: true
    action: "block"
    allowed:
      - path: "/tmp"
        process_path: "*"
        uid: 4294967295
        is_dir: true
      - path: "/home/<USER>/workspace"
        process_path: "*"
        uid: 1000
        is_dir: true
      - path: "/var/log/myapp.log"
        process_path: "/usr/bin/myapp"
        uid: 1000
```

## Complete Configuration Examples

### Basic Protection Setup
```yaml
file_access:
  - rule_id: "basic_protection"
    enabled: true
    rules:
      - path: "/etc/passwd"
        access: "write"
        action: "block"
        allowed:
          - process: "/usr/bin/passwd"
            uid: 0
      - path: "/home/<USER>/documents"
        access: "read"
        action: "audit"
        dir: true

filesystem_protect:
  - rule_id: "global_protection"
    enabled: false
    action: "block"
```

### Advanced Multi-Rule Configuration
```yaml
file_access:
  - rule_id: "system_files"
    enabled: true
    rules:
      - path: "/etc/shadow"
        sub_rule_id: "shadow_write"
        access: "write"
        action: "block"
        allowed:
          - process: "/usr/bin/passwd"
            uid: 0
      - path: "/etc/hosts"
        sub_rule_id: "hosts_write"
        access: "write"
        action: "audit"
        allowed:
          - process: "*"
            uid: 0

  - rule_id: "application_files"
    enabled: true
    rules:
      - path: "/opt/webapp/config"
        sub_rule_id: "webapp_config"
        access: "write"
        action: "block"
        dir: true
        allowed:
          - process: "/opt/webapp/bin/webapp"
            uid: 1001

filesystem_protect:
  - rule_id: "strict_protection"
    enabled: true
    action: "block"
    allowed:
      - path: "/tmp"
        process_path: "*"
        uid: 4294967295
        is_dir: true
      - path: "/var/tmp"
        process_path: "*"
        uid: 4294967295
        is_dir: true
```

## Configuration Best Practices

### 1. Rule Organization
- Use descriptive `rule_id` values
- Group related rules together
- Use `sub_rule_id` for complex rule sets

### 2. Path Specifications
- Always use absolute paths
- Be specific with file paths to avoid unintended matches
- Use directory rules carefully with `dir: true`

### 3. Exception Handling
- Start with restrictive rules and add exceptions as needed
- Use specific process paths when possible
- Avoid wildcard UIDs unless necessary

### 4. Performance Considerations
- Minimize the number of monitored paths
- Use `audit` action for high-frequency operations
- Consider the impact of directory-level rules

### 5. Security Guidelines
- Protect configuration files (readable only by root)
- Test configurations before deployment
- Monitor logs for unexpected access patterns
- Regularly review and update rules

## Configuration Validation

### Test Configuration
```bash
sudo fim-ctl test /path/to/config.yaml
```

### Common Validation Errors

#### Invalid YAML Syntax
```
Error: YAML parsing failed at line X
```
**Solution**: Check YAML syntax, indentation, and quotes.

#### Missing Required Fields
```
Error: Missing required field 'path' in rule
```
**Solution**: Ensure all required fields are present.

#### Invalid Values
```
Error: Invalid action 'monitor', must be 'block' or 'audit'
```
**Solution**: Use only supported values as documented.

## Dynamic Configuration

FIM currently requires a restart to apply configuration changes:

```bash
sudo systemctl restart fim
# or
sudo fim-ctl stop
sudo fim-ctl start /etc/fim/policy.yaml
```

## Troubleshooting Configuration Issues

### Enable Debug Logging
```bash
export FIM_DEBUG=1
sudo fim-ctl start /etc/fim/policy.yaml
```

### Check Rule Application
Monitor logs to verify rules are being applied:
```bash
sudo journalctl -u fim -f | grep "Added file_access rule"
```

### Verify File Paths
Ensure monitored files exist and paths are correct:
```bash
ls -la /path/to/monitored/file
```

## Configuration Templates

### Web Server Protection
```yaml
file_access:
  - rule_id: "web_server_protection"
    enabled: true
    rules:
      - path: "/var/www/html"
        access: "write"
        action: "block"
        dir: true
        allowed:
          - process: "/usr/bin/rsync"
            uid: 0
          - process: "/usr/bin/git"
            uid: 1000
      - path: "/etc/nginx"
        access: "write"
        action: "audit"
        dir: true
        allowed:
          - process: "*"
            uid: 0
```

### Database Protection
```yaml
file_access:
  - rule_id: "database_protection"
    enabled: true
    rules:
      - path: "/var/lib/mysql"
        access: "write"
        action: "block"
        dir: true
        allowed:
          - process: "/usr/sbin/mysqld"
            uid: 999
      - path: "/etc/mysql"
        access: "write"
        action: "audit"
        dir: true
        allowed:
          - process: "*"
            uid: 0
```

### Development Environment
```yaml
file_access:
  - rule_id: "dev_environment"
    enabled: true
    rules:
      - path: "/home/<USER>/projects"
        access: "write"
        action: "audit"
        dir: true
        allowed:
          - process: "*"
            uid: 1000
      - path: "/home/<USER>/.ssh"
        access: "read"
        action: "audit"
        dir: true
      - path: "/home/<USER>/.ssh"
        access: "write"
        action: "block"
        dir: true
        allowed:
          - process: "/usr/bin/ssh-keygen"
            uid: 1000
```
