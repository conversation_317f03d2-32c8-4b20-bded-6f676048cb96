#ifndef FIM_UTILS_H
#define FIM_UTILS_H

#include "fim_common.h"

/* ========== String Utilities ========== */

/**
 * Extract filename from full path
 * @param path: Full file path
 * @return: Pointer to filename part (not allocated, points into path)
 */
const char* extract_filename(const char* path);

/**
 * Safe string copy with bounds checking
 * @param dest: Destination buffer
 * @param src: Source string
 * @param dest_size: Size of destination buffer
 * @return: 0 on success, -1 on error
 */
int safe_strncpy(char *dest, const char *src, size_t dest_size);

/**
 * Check if two filenames match
 * @param rule_path: Path from rule
 * @param event_filename: Filename from event
 * @return: 1 if match, 0 if not
 */
int is_filename_match(const char* rule_path, const char* event_filename);

/* ========== File System Utilities ========== */

/**
 * Get inode number for a path
 * @param path: File path
 * @param inode: Output inode number
 * @return: 0 on success, -1 on error
 */
int get_path_inode(const char *path, __u32 *inode);

/**
 * Get device number and inode for a path
 * @param path: File path
 * @param dev: Output device number
 * @param inode: Output inode number
 * @return: 0 on success, -1 on error
 */
int get_path_dev_inode(const char *path, __u32 *dev, __u32 *inode);

/**
 * Check if path exists
 * @param path: File path to check
 * @return: 1 if exists, 0 if not
 */
int path_exists(const char *path);

/* ========== User/Process Utilities ========== */

/**
 * Get username from UID
 * @param uid: User ID
 * @return: Username string (static buffer)
 */
const char *get_username_from_uid(__u32 uid);

/**
 * Get UID from username
 * @param username: Username string
 * @return: UID or UINT32_MAX on error
 */
__u32 get_uid_from_username(const char *username);

/* ========== Operation/Action Name Utilities ========== */

/**
 * Get operation name string
 * @param operation: Operation code
 * @return: Operation name string
 */
const char *get_operation_name(__u32 operation);

/**
 * Get action name string
 * @param action: Action code
 * @return: Action name string
 */
const char *get_action_name(__u32 action);

/**
 * Get result name string
 * @param result: Result code
 * @return: Result name string
 */
const char *get_result_name(__u32 result);

/* ========== Rule Management Utilities ========== */

// Legacy rule management functions - removed

/* ========== Time Utilities ========== */

/**
 * Format timestamp for logging
 * @param timestamp: Timestamp in nanoseconds
 * @param buffer: Output buffer
 * @param buffer_size: Size of output buffer
 */
void format_timestamp(__u64 timestamp, char *buffer, size_t buffer_size);

#endif /* FIM_UTILS_H */
