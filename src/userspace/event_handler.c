#include "event_handler.h"
#include "utils.h"
#include <time.h>
#include "ebpf_manager.h"

/* External references */
extern struct fim_new_config *current_fim_config;

/* ========== Global Variables ========== */
static struct ring_buffer *log_rb = NULL;
static struct ring_buffer *inode_rb = NULL;
static pthread_t monitoring_thread;
static volatile int monitoring_active = 0;

/* Event statistics */
static __u64 total_events = 0;
static __u64 blocked_events = 0;
static __u64 monitored_events = 0;

/* ========== Event Processing ========== */

int event_handler_init(void) {
    FIM_LOG_INFO("Initializing event handler...");

    // Initialize logging system
    int result = logger_init();
    if (result != FIM_SUCCESS) {
        return result;
    }

    // Check if eBPF program is loaded
    if (!ebpf_is_loaded()) {
        FIM_LOG_ERROR("eBPF program not loaded, cannot initialize event handler");
        return FIM_ERROR_EBPF;
    }

    // Create ring buffer for log events
    int log_events_fd = ebpf_get_map_fd("log_events");
    if (log_events_fd >= 0) {
        log_rb = ring_buffer__new(log_events_fd, handle_log_event, NULL, NULL);
        if (!log_rb) {
            FIM_LOG_ERROR("Failed to create ring buffer for log events");
            return FIM_ERROR_EBPF;
        }
    } else {
    }

    // Create ring buffer for inode events
    int inode_events_fd = ebpf_get_map_fd("inode_events");
    if (inode_events_fd >= 0) {
        inode_rb = ring_buffer__new(inode_events_fd, handle_inode_event, NULL, NULL);
        if (!inode_rb) {
            FIM_LOG_ERROR("Failed to create ring buffer for inode events");
            if (log_rb) {
                ring_buffer__free(log_rb);
                log_rb = NULL;
            }
            return FIM_ERROR_EBPF;
        }
    } else {
    }

    if (!log_rb && !inode_rb) {
        FIM_LOG_ERROR("No ring buffers could be created");
        return FIM_ERROR_EBPF;
    }

    FIM_LOG_INFO("Event handler initialized successfully");
    return FIM_SUCCESS;
}

void event_handler_cleanup(void) {

    // Stop monitoring
    event_handler_stop();

    // Free ring buffers
    if (log_rb) {
        ring_buffer__free(log_rb);
        log_rb = NULL;
    }

    if (inode_rb) {
        ring_buffer__free(inode_rb);
        inode_rb = NULL;
    }

    // Cleanup logging
    logger_cleanup();
}

int event_handler_start(void) {
    if (!log_rb && !inode_rb) {
        FIM_LOG_ERROR("No ring buffers available for event processing");
        return FIM_ERROR_EBPF;
    }

    FIM_LOG_INFO("Starting event processing...");

    // Start monitoring thread
    int result = start_realtime_monitoring();
    if (result != FIM_SUCCESS) {
        return result;
    }

    // Main event loop
    while (running && !stop_requested) {
        int events_processed = 0;

        // Poll log events
        if (log_rb) {
            int ret = ring_buffer__poll(log_rb, RING_BUFFER_POLL_TIMEOUT_MS);
            if (ret > 0) {
                events_processed += ret;
            }
        }

        // Poll inode events
        if (inode_rb) {
            int ret = ring_buffer__poll(inode_rb, RING_BUFFER_POLL_TIMEOUT_MS);
            if (ret > 0) {
                events_processed += ret;
            }
        }

        // Small delay if no events
        if (events_processed == 0) {
            usleep(EVENT_LOOP_SLEEP_US);
        }
    }

    // Stop monitoring
    stop_realtime_monitoring();

    FIM_LOG_INFO("Event processing stopped");
    return FIM_SUCCESS;
}

void event_handler_stop(void) {
    stop_requested = 1;
    stop_realtime_monitoring();
}

/* ========== Log Event Handling ========== */

int handle_log_event(void *ctx, void *data, size_t data_sz) {
    if (!data || data_sz < sizeof(struct fim_log_event)) {
        return -1;
    }

    struct fim_log_event *event = (struct fim_log_event *)data;

    // Update statistics
    total_events++;
    if (event->action == ACTION_BLOCK) {
        blocked_events++;
    } else if (event->action == ACTION_AUDIT) {
        monitored_events++;
    }

    // Process event
    process_log_event(event);

    return 0;
}

void process_log_event(struct fim_log_event *event) {
    if (!event) {
        return;
    }

    // Log to file
    log_event_to_file(event);


}

/* ========== Inode Event Handling ========== */

int handle_inode_event(void *ctx, void *data, size_t data_sz) {
    if (!data || data_sz < sizeof(struct simple_file_event)) {
        return -1;
    }

    struct simple_file_event *event = (struct simple_file_event *)data;
    process_inode_event(event);

    return 0;
}

void process_inode_event(struct simple_file_event *event) {
    if (!event) {
        return;
    }

    // Handle inode updates for new configuration format
    if (current_fim_config) {
        FIM_LOG_INFO("File '%s' changed (type: %u), updating inode mappings",
                    event->filename, event->event_type);

        // Reload the current configuration to update inode mappings
        int result = ebpf_apply_new_config(current_fim_config);
        if (result < 0) {
            FIM_LOG_ERROR("Failed to update inode mappings after file change");
        }
    }
}

/* ========== Logging System ========== */

int logger_init(void) {
    // For now, just use stdout/stderr
    // In a full implementation, this would open log files
    return FIM_SUCCESS;
}

void logger_cleanup(void) {
    // Close log files if any were opened
}

void log_event_to_file(struct fim_log_event *event) {
    if (!event) {
        return;
    }

    // Format current timestamp (in user space)
    char timestamp_str[64];
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);
    strftime(timestamp_str, sizeof(timestamp_str), "%Y-%m-%d %H:%M:%S", tm_info);

    // Get username
    const char *username = get_username_from_uid(event->uid);

    // Log to stdout for now (in production, this would go to a log file)
    printf("[%s] %s %s %s by %s (PID: %u, dev: %u, inode: %u)\n",
           timestamp_str,
           get_operation_name(event->operation),
           get_action_name(event->action),
           event->target_path,
           username,
           event->pid,
           event->dev,
           event->inode);
}

void log_event_to_console(struct fim_log_event *event) {
    log_event_to_file(event); // Same implementation for now
}

/* ========== Event Statistics ========== */

void get_event_statistics(__u64 *total, __u64 *blocked, __u64 *monitored) {
    if (total) *total = total_events;
    if (blocked) *blocked = blocked_events;
    if (monitored) *monitored = monitored_events;
}

void reset_event_statistics(void) {
    total_events = 0;
    blocked_events = 0;
    monitored_events = 0;
}

void print_event_statistics(void) {
    printf("\n=== Event Statistics ===\n");
    printf("Total events:     %llu\n", total_events);
    printf("Blocked events:   %llu\n", blocked_events);
    printf("Monitored events: %llu\n", monitored_events);
    printf("========================\n\n");
}

/* ========== Real-time Monitoring ========== */

int start_realtime_monitoring(void) {
    if (monitoring_active) {
        return FIM_SUCCESS; // Already running
    }

    monitoring_active = 1;

    int ret = pthread_create(&monitoring_thread, NULL, event_monitoring_thread, NULL);
    if (ret != 0) {
        FIM_LOG_ERROR("Failed to create monitoring thread: %s", strerror(ret));
        monitoring_active = 0;
        return FIM_ERROR_EBPF;
    }


    return FIM_SUCCESS;
}

void stop_realtime_monitoring(void) {
    if (!monitoring_active) {
        return;
    }

    monitoring_active = 0;
    pthread_join(monitoring_thread, NULL);

}

void *event_monitoring_thread(void *arg) {

    while (monitoring_active && !stop_requested) {
        // This thread could be used for additional background processing
        // For now, just sleep
        sleep(1);
    }


    return NULL;
}
