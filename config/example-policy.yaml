# FIM策略配置示例
# 展示如何配置不同类型的规则和异常

file:
  enable: true
  rule_id: "fim_example_policy"

  # 读操作规则
  read_rules:
    sub_rule_id: "read_rules_001"
    
    # 拒绝列表
    deny_list:
      enable: true
      rules:
      
      # 示例1: 保护敏感配置文件
      - path: "/etc/passwd"
        dir: false
        action: "block"
        grad_rule_id: "read_deny_passwd"
        excepts:
          - process: ["/usr/bin/id", "/usr/bin/whoami", "/bin/login"]
          
      # 示例2: 保护SSH配置
      - path: "/etc/ssh"
        dir: true
        action: "block"
        grad_rule_id: "read_deny_ssh_config"
        excepts:
          - process: ["/usr/sbin/sshd", "/usr/bin/ssh"]
          - user: ["admin"]
          
      # 示例3: 保护用户私有目录
      - path: "/home/<USER>"
        dir: true
        action: "block"
        grad_rule_id: "read_deny_sensitive_home"
        excepts:
          - user: ["sensitive-user", "backup"]
          
      # 示例4: 保护系统日志
      - path: "/var/log/auth.log"
        dir: false
        action: "block"
        grad_rule_id: "read_deny_auth_log"
        excepts:
          - process: ["/usr/bin/tail", "/usr/bin/less", "/usr/sbin/rsyslog"]
          - user: ["syslog", "admin"]

  # 写操作规则
  write_rules:
    sub_rule_id: "write_rules_001"
    
    # 拒绝列表
    deny_list:
      enable: true
      rules:
      
      # 示例1: 保护系统配置文件
      - path: "/etc/passwd"
        dir: false
        action: "block"
        sub_rule_id: "write_deny_passwd"
        excepts:
          - process: ["/usr/sbin/useradd", "/usr/sbin/userdel", "/usr/sbin/usermod"]
          
      # 示例2: 保护启动目录
      - path: "/boot"
        dir: true
        action: "block"
        sub_rule_id: "write_deny_boot"
        excepts:
          - process: ["/usr/sbin/grub-install", "/usr/sbin/update-grub"]
          - user: ["root"]  # 仅在维护时启用
          
      # 示例3: 保护系统二进制文件
      - path: "/usr/bin"
        dir: true
        action: "block"
        sub_rule_id: "write_deny_usr_bin"
        excepts:
          - process: ["/usr/bin/apt", "/usr/bin/dpkg", "/usr/bin/yum"]
          
      # 示例4: 保护重要配置目录
      - path: "/etc/systemd"
        dir: true
        action: "block"
        sub_rule_id: "write_deny_systemd"
        excepts:
          - process: ["/usr/bin/systemctl", "/usr/sbin/systemd"]
          - user: ["admin"]
          
      # 示例5: 保护数据库目录
      - path: "/var/lib/mysql"
        dir: true
        action: "block"
        sub_rule_id: "write_deny_mysql"
        excepts:
          - process: ["/usr/sbin/mysqld", "/usr/bin/mysql"]
          - user: ["mysql"]

    # 允许列表（白名单模式，当前未启用）
    allow_list:
      enable: false
      action: "audit"
      rules:
      # 示例：仅允许写入临时目录
      - path: "/tmp"
        dir: true
        grad_rule_id: "write_allow_tmp"
      - path: "/var/tmp"
        dir: true
        grad_rule_id: "write_allow_var_tmp"

# 策略元数据
metadata:
  version: "2.1"
  created: "2025-07-08"
  updated: "2025-07-08"
  author: "Security Team"
  environment: "production"
  description: "示例FIM策略，展示各种规则和异常配置"
  
  # 实现状态
  implementation:
    read_write_separation: true
    inode_based_filtering: true
    block_action: true
    exception_support: true
    audit_action: false  # 计划功能
    allow_list_support: false  # 计划功能
