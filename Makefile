# FIM (File Integrity Monitoring) Makefile

CC := clang
LLVM_STRIP := llvm-strip
ARCH := $(shell uname -m | sed 's/x86_64/x86/' | sed 's/aarch64/arm64/')

SRC_DIR := src
EBPF_DIR := $(SRC_DIR)/ebpf
USER_DIR := $(SRC_DIR)/userspace
INCLUDE_DIR := $(SRC_DIR)/include
BUILD_DIR := build
BIN_DIR := bin

EBPF_CFLAGS := -O2 -g -Wall -Werror
EBPF_CFLAGS += -target bpf -D__TARGET_ARCH_$(ARCH)
EBPF_CFLAGS += -I$(INCLUDE_DIR) -I$(EBPF_DIR) -I/usr/include/$(shell uname -m)-linux-gnu

USER_CFLAGS := -O2 -g -Wall -Werror
USER_CFLAGS += -I$(INCLUDE_DIR)
USER_LDFLAGS := -lbpf -lelf -lz -lyaml

# Source files
EBPF_SOURCES := $(EBPF_DIR)/fim_lsm.c
USER_SOURCES := $(USER_DIR)/main.c $(USER_DIR)/config.c $(USER_DIR)/utils.c \
                $(USER_DIR)/ebpf_manager.c $(USER_DIR)/event_handler.c

# Object files
USER_OBJECTS := $(USER_SOURCES:$(USER_DIR)/%.c=$(BUILD_DIR)/%.o)

EBPF_TARGETS := $(BUILD_DIR)/fim_lsm.o
USER_TARGETS := $(BIN_DIR)/fim-ctl

.PHONY: all clean modules legacy

all: $(EBPF_TARGETS) $(USER_TARGETS)

# Legacy target for backward compatibility
legacy: $(EBPF_TARGETS) $(BIN_DIR)/fim-ctl-legacy

$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)

$(BIN_DIR):
	mkdir -p $(BIN_DIR)

# eBPF target
$(BUILD_DIR)/fim_lsm.o: $(EBPF_DIR)/fim_lsm.c $(EBPF_DIR)/fim_common.h | $(BUILD_DIR)
	$(CC) $(EBPF_CFLAGS) -c $< -o $@
	$(LLVM_STRIP) -g $@

# Modular userspace target
$(BUILD_DIR)/%.o: $(USER_DIR)/%.c | $(BUILD_DIR)
	$(CC) $(USER_CFLAGS) -c $< -o $@

$(BIN_DIR)/fim-ctl: $(USER_OBJECTS) | $(BIN_DIR)
	$(CC) $(USER_OBJECTS) -o $@ $(USER_LDFLAGS)

# Legacy monolithic target
$(BIN_DIR)/fim-ctl-legacy: $(USER_DIR)/fim-ctl-legacy.c | $(BIN_DIR)
	$(CC) $(USER_CFLAGS) $< -o $@ $(USER_LDFLAGS)

clean:
	rm -rf $(BUILD_DIR) $(BIN_DIR)
