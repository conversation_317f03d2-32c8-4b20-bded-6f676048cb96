/*
 * File Integrity Monitoring (FIM) - eBPF LSM Implementation
 *
 * This program implements file integrity monitoring using Linux Security Module (LSM)
 * hooks and kprobes to detect file operations on monitored files.
 *
 * Key Features:
 * - File creation/deletion/rename monitoring via LSM hooks and kprobes
 * - Dynamic inode mapping updates for real-time tracking
 * - Configurable monitoring policies with allow/deny rules
 * - Real-time access logging with detailed operation information
 *
 * Architecture:
 * - LSM hooks: Monitor file access, creation, deletion, and rename operations
 * - kprobe: Detect new file instantiation for inode mapping updates
 * - Ring buffer: Communicate inode changes to userspace for map updates
 * - eBPF maps: Store monitoring rules and inode mappings
 */

#include "fim_common.h"

char LICENSE[] SEC("license") = "GPL";

/* ========== Constants and Definitions ========== */

/* Event types for userspace communication */
#define EVENT_TYPE_CREATE   1
#define EVENT_TYPE_DELETE   2

/* Special inode value for non-existent files */
#define SPECIAL_INODE_VALUE UINT32_MAX

/* ========== Utility Functions ========== */

/**
 * extract_filename_from_dentry - Extract filename from dentry structure
 * @dentry: Dentry structure pointer
 * @filename: Output buffer for filename
 * @max_len: Maximum length of output buffer
 * @return: 0 on success, -1 on failure
 */
static __always_inline int extract_filename_from_dentry(struct dentry *dentry, char *filename, int max_len) {
    if (!dentry) {
        return -1;
    }

    const unsigned char *name = BPF_CORE_READ(dentry, d_name.name);
    if (!name) {
        return -1;
    }

    long ret = bpf_probe_read_kernel_str(filename, max_len, (const char *)name);
    if (ret <= 0) {
        return -1;
    }

    filename[max_len - 1] = '\0';
    return 0;
}

/* ========== File Monitoring Functions ========== */

/**
 * is_filename_monitored - Check if filename is in monitoring list
 * @filename: Filename to check
 * @return: 1 if monitored, 0 otherwise
 */
static __always_inline int is_filename_monitored(const char *filename) {
    if (!filename) {
        return 0;
    }

    char map_key[64] = {0};
    int len = 0;

    /* Copy filename to map key */
    for (int i = 0; i < 63 && filename[i] != '\0'; i++) {
        map_key[i] = filename[i];
        len++;
    }
    map_key[len] = '\0';

    /* Lookup in monitored filenames map */
    __u32 *flag = bpf_map_lookup_elem(&monitored_filenames_map, map_key);
    return flag ? 1 : 0;
}

/**
 * should_monitor_dentry - Check if dentry corresponds to a monitored file
 * @dentry: Dentry structure to check
 * @return: 1 if should be monitored, 0 otherwise
 */
static __always_inline int should_monitor_dentry(struct dentry *dentry) {
    char filename[64] = {0};
    if (extract_filename_from_dentry(dentry, filename, sizeof(filename)) != 0) {
        return 0;
    }
    return is_filename_monitored(filename);
}

/* ========== Logging Functions ========== */

/**
 * log_access_event - Log file access event to ring buffer
 * @dentry: Dentry of the accessed file
 * @operation: Type of operation (read/write/remove)
 * @action: Action taken (audit/block)
 * @file: File structure (optional)
 */
static __always_inline void log_access_event(struct dentry *dentry, __u32 operation,
                                            __u32 action, struct file *file) {
    struct fim_log_event *event;

    /* Reserve space in ring buffer */
    event = bpf_ringbuf_reserve(&log_events, sizeof(*event), 0);
    if (!event) {
        return;
    }

    /* Fill event information - timestamp added in userspace */
    event->pid = bpf_get_current_pid_tgid() >> 32;
    event->uid = bpf_get_current_uid_gid() & 0xFFFFFFFF;
    event->operation = operation;
    event->action = action;

    /* Get file device and inode */
    if (dentry) {
        struct inode *inode = BPF_CORE_READ(dentry, d_inode);
        if (inode) {
            dev_t dev_raw = BPF_CORE_READ(inode, i_sb, s_dev);
            event->dev = (__u32)dev_raw;
            event->inode = BPF_CORE_READ(inode, i_ino);
        } else {
            event->dev = 0;
            event->inode = 0;
        }
    } else {
        event->dev = 0;
        event->inode = 0;
    }

    /* Get process name */
    bpf_get_current_comm(event->comm, sizeof(event->comm));

    /* Get path information - prefer file->f_path */
    int path_success = 0;
    if (file) {
        struct path *file_path_ptr = &file->f_path;
        long ret = bpf_d_path(file_path_ptr, event->target_path, sizeof(event->target_path));
        if (ret >= 0) {
            path_success = 1;
        }
    }

    /* Fallback to filename if no file or bpf_d_path failed */
    if (!path_success && dentry) {
        const unsigned char *name = BPF_CORE_READ(dentry, d_name.name);
        if (name) {
            bpf_probe_read_kernel_str(event->target_path, sizeof(event->target_path), (const char *)name);
        } else {
            event->target_path[0] = '\0';
        }
    }

    /* Submit event to userspace */
    bpf_ringbuf_submit(event, 0);
}

/* ========== New Access Control Functions ========== */

/**
 * get_current_process_dev_inode - Get current process executable device and inode
 * @dev: Output device number
 * @inode: Output inode number
 * @return: 0 on success, -1 on error
 */
static __always_inline int get_current_process_dev_inode(__u32 *dev, __u32 *inode) {
    if (!dev || !inode) {
        return -1;
    }

    struct task_struct *task = (struct task_struct *)bpf_get_current_task();
    if (!task) {
        *dev = DEVICE_ANY;
        *inode = PROCESS_ANY;
        return -1;
    }

    struct mm_struct *mm = BPF_CORE_READ(task, mm);
    if (!mm) {
        *dev = DEVICE_ANY;
        *inode = PROCESS_ANY;
        return -1;
    }

    struct file *exe_file = BPF_CORE_READ(mm, exe_file);
    if (!exe_file) {
        *dev = DEVICE_ANY;
        *inode = PROCESS_ANY;
        return -1;
    }

    struct dentry *dentry = BPF_CORE_READ(exe_file, f_path.dentry);
    if (!dentry) {
        *dev = DEVICE_ANY;
        *inode = PROCESS_ANY;
        return -1;
    }

    struct inode *inode_ptr = BPF_CORE_READ(dentry, d_inode);
    if (!inode_ptr) {
        *dev = DEVICE_ANY;
        *inode = PROCESS_ANY;
        return -1;
    }

    dev_t dev_raw = BPF_CORE_READ(inode_ptr, i_sb, s_dev);
    *dev = (__u32)dev_raw;
    *inode = BPF_CORE_READ(inode_ptr, i_ino);
    return 0;
}

/**
 * get_current_process_inode - Legacy function for backward compatibility
 * @return: Process executable inode or 0 if not found
 */
static __always_inline __u32 __attribute__((unused)) get_current_process_inode(void) {
    __u32 dev, inode;
    if (get_current_process_dev_inode(&dev, &inode) == 0) {
        return inode;
    }
    return 0;
}

/**
 * check_file_access_by_inode - Check file access using new configuration format with directory traversal
 * @dentry: Dentry of the file being accessed (for directory traversal)
 * @access_type: Access type (ACCESS_READ/ACCESS_WRITE)
 * @process_dev: Process executable device number
 * @process_inode: Process executable inode
 * @uid: User ID
 * @return: 0=allow, ACTION_BLOCK=block, ACTION_AUDIT=audit, RULE_NOT_FOUND=not found
 */
static __always_inline int check_file_access_by_inode(struct dentry *dentry, __u32 access_type,
                                                     __u32 process_dev, __u32 process_inode, __u32 uid) {
    if (!dentry) {
        return RULE_NOT_FOUND;
    }

    struct dentry *cur = dentry;

    /* Traverse directory hierarchy up to 4 levels */
    for (int i = 0; i < 4; i++) {
        if (!cur) {
            break;
        }

        struct inode *inode = BPF_CORE_READ(cur, d_inode);
        if (!inode) {
            break;
        }

        dev_t dev_raw = BPF_CORE_READ(inode, i_sb, s_dev);
        __u32 current_dev = (__u32)dev_raw;
        __u32 current_inode = BPF_CORE_READ(inode, i_ino);

        /* Construct lookup key for current level */
        struct file_access_key fa_key = {
            .dev = current_dev,
            .inode = current_inode,
            .access_type = access_type
        };



        /* Look up rule at current level */
        struct file_access_value *fa_value = bpf_map_lookup_elem(&file_access_map, &fa_key);

        if (fa_value && fa_value->rule_enabled) {

            /* Found a rule, check for exceptions */
            if (fa_value->has_exceptions) {
                struct file_access_exception_key ex_key = {
                    .dev = current_dev,
                    .inode = current_inode,
                    .access_type = access_type,
                    .process_dev = process_dev,
                    .process_inode = process_inode,
                    .uid = uid
                };

                /* Check exact match exception */

                struct file_access_exception_value *ex_value = bpf_map_lookup_elem(&file_access_exc, &ex_key);
                if (ex_value && ex_value->allowed) {

                    return 0; // Exception allows access
                }

                // Check process wildcard exception
                ex_key.process_dev = DEVICE_ANY;
                ex_key.process_inode = PROCESS_ANY;
                ex_value = bpf_map_lookup_elem(&file_access_exc, &ex_key);
                if (ex_value && ex_value->allowed) {
                    return 0;
                }

                // Check UID wildcard exception
                ex_key.process_dev = process_dev;
                ex_key.process_inode = process_inode;
                ex_key.uid = UID_ANY;
                ex_value = bpf_map_lookup_elem(&file_access_exc, &ex_key);
                if (ex_value && ex_value->allowed) {
                    return 0;
                }

                // Check double wildcard exception
                ex_key.process_dev = DEVICE_ANY;
                ex_key.process_inode = PROCESS_ANY;
                ex_key.uid = UID_ANY;
                ex_value = bpf_map_lookup_elem(&file_access_exc, &ex_key);
                if (ex_value && ex_value->allowed) {
                    return 0;
                }
            }

            // No exception found, return the rule action



            return fa_value->action;
        }

        /* Move to parent directory */
        struct dentry *parent = BPF_CORE_READ(cur, d_parent);
        if (parent == cur) {
            break; /* Reached root */
        }
        cur = parent;
    }

    return RULE_NOT_FOUND;
}

/**
 * check_filesystem_protect_by_inode - Global filesystem read-only protection
 * Default: Block all write operations (global read-only)
 * Exception: Allow write only for files/processes explicitly listed in allowed list
 * @file_dev: File device number
 * @file_inode: File inode
 * @process_dev: Process executable device number
 * @process_inode: Process executable inode
 * @uid: User ID
 * @return: 0=allow write, ACTION_BLOCK=block write, ACTION_AUDIT=audit write
 */
static __always_inline int check_filesystem_protect_by_inode(__u32 file_dev, __u32 file_inode,
                                                           __u32 process_dev, __u32 process_inode, __u32 uid) {
    // 1. Check if filesystem protection is enabled
    __u32 config_key = 0;
    struct filesystem_protect_config *config = bpf_map_lookup_elem(&filesystem_prot, &config_key);
    if (!config || !config->enabled) {
        return 0; // Not enabled, allow all writes
    }

    // 2. Global read-only protection: Check for write exceptions
    // Only files/processes in the exception list are allowed to write
    struct filesystem_exception_key ex_key = {
        .dev = file_dev,
        .inode = file_inode,
        .process_dev = process_dev,
        .process_inode = process_inode,
        .uid = uid
    };

    // Check exact match exception (specific file + process + user)
    struct filesystem_exception_value *ex_value = bpf_map_lookup_elem(&filesystem_exce, &ex_key);
    if (ex_value && ex_value->allowed) {
        return 0; // Exception found, allow write
    }

    // Check process wildcard exception (specific file + any process + user)
    ex_key.process_dev = DEVICE_ANY;
    ex_key.process_inode = PROCESS_ANY;
    ex_value = bpf_map_lookup_elem(&filesystem_exce, &ex_key);
    if (ex_value && ex_value->allowed) {
        return 0; // Process wildcard exception, allow write
    }

    // Check UID wildcard exception (specific file + process + any user)
    ex_key.process_dev = process_dev;
    ex_key.process_inode = process_inode;
    ex_key.uid = UID_ANY;
    ex_value = bpf_map_lookup_elem(&filesystem_exce, &ex_key);
    if (ex_value && ex_value->allowed) {
        return 0; // UID wildcard exception, allow write
    }

    // Check double wildcard exception (specific file + any process + any user)
    ex_key.process_dev = DEVICE_ANY;
    ex_key.process_inode = PROCESS_ANY;
    ex_key.uid = UID_ANY;
    ex_value = bpf_map_lookup_elem(&filesystem_exce, &ex_key);
    if (ex_value && ex_value->allowed) {
        return 0; // Double wildcard exception, allow write
    }

    // No exception found: Apply global read-only protection
    // Block write operation (default action should be ACTION_BLOCK for read-only protection)
    return config->default_action;
}

/* ========== LSM Hook Implementations ========== */

/**
 * fim_file_open - LSM hook for file open operations
 * Monitors and controls file access based on configured rules
 * Uses new two-stage checking: file_access rules first, then filesystem_protect
 */
SEC("lsm/file_open")
int BPF_PROG(fim_file_open, struct file *file) {
    struct dentry *dentry = BPF_CORE_READ(file, f_path.dentry);
    if (!dentry) {
        return 0;
    }

    struct inode *inode_ptr = BPF_CORE_READ(dentry, d_inode);
    if (!inode_ptr) {
        return 0;
    }

    dev_t dev_raw = BPF_CORE_READ(inode_ptr, i_sb, s_dev);
    __u32 file_dev = (__u32)dev_raw;
    __u32 file_inode = BPF_CORE_READ(inode_ptr, i_ino);

    // Debug: print the values
    __u32 f_mode = BPF_CORE_READ(file, f_mode);
    __u32 access_type;
    __u32 uid = bpf_get_current_uid_gid() & 0xFFFFFFFF;

    __u32 process_dev, process_inode;
    get_current_process_dev_inode(&process_dev, &process_inode);







    // Determine access type
    if (f_mode & FMODE_WRITE) {
        access_type = ACCESS_WRITE;
    } else {
        access_type = ACCESS_READ;
    }



    // Priority-based policy checking
    // Priority: file_access_write > file_access_read > filesystem_protect

    if (access_type == ACCESS_WRITE) {
        // For WRITE operations: check file_access write rules, then filesystem_protect

        // Priority 1: Check file_access WRITE rules (highest priority)

        int write_result = check_file_access_by_inode(dentry, ACCESS_WRITE, process_dev, process_inode, uid);
        if (write_result != RULE_NOT_FOUND) {
            if (write_result == ACTION_BLOCK) {
                log_access_event(dentry, FIM_OP_WRITE, ACTION_BLOCK, file);
                return -EACCES; // Block write access
            } else if (write_result == ACTION_AUDIT) {
                log_access_event(dentry, FIM_OP_WRITE, ACTION_AUDIT, file);
                return 0; // Allow write access with audit
            }
            // Allow (from exception)
            return 0;
        }



        // Priority 2: Check filesystem_protect (lower priority for write)
        int fs_protect_result = check_filesystem_protect_by_inode(file_dev, file_inode, process_dev, process_inode, uid);
        if (fs_protect_result != 0) {
            if (fs_protect_result == ACTION_BLOCK) {
                log_access_event(dentry, FIM_OP_WRITE, ACTION_BLOCK, file);
                return -EACCES; // Block write access
            } else if (fs_protect_result == ACTION_AUDIT) {
                log_access_event(dentry, FIM_OP_WRITE, ACTION_AUDIT, file);
                return 0; // Allow write access with audit
            }
        }

    }
    else if (access_type == ACCESS_READ) {
        // For READ operations: only check file_access read rules
        // filesystem_protect is for write protection only

        // Check file_access READ rules
        int read_result = check_file_access_by_inode(dentry, ACCESS_READ, process_dev, process_inode, uid);
        if (read_result != RULE_NOT_FOUND) {
            if (read_result == ACTION_BLOCK) {
                log_access_event(dentry, FIM_OP_READ, ACTION_BLOCK, file);
                return -EACCES; // Block read access
            } else if (read_result == ACTION_AUDIT) {
                log_access_event(dentry, FIM_OP_READ, ACTION_AUDIT, file);
                return 0; // Allow read access with audit
            }
            return 0; // Allow (from exception)
        }

        // No read rules found, default allow (no log)
        return 0;
    }

    return 0; // Default allow - only explicitly configured files are protected
}

/**
 * fim_inode_create - LSM hook for inode creation operations
 * Monitors and controls file creation based on configured rules
 */
SEC("lsm/inode_create")
int BPF_PROG(fim_inode_create, struct inode *dir, struct dentry *dentry, umode_t mode) {
    if (!dentry) return 0;

    __u32 process_dev, process_inode;
    get_current_process_dev_inode(&process_dev, &process_inode);

    int result = check_file_access_by_inode(dentry, ACCESS_WRITE, process_dev, process_inode, bpf_get_current_uid_gid() & 0xFFFFFFFF);

    if (result == ACTION_BLOCK) {
        return -EACCES; // Block creation
    }

    // Monitor mode: log creation and send inode update event if needed
    if (should_monitor_dentry(dentry)) {
        char filename[64] = {0};
        if (extract_filename_from_dentry(dentry, filename, sizeof(filename)) == 0) {
            struct simple_file_event *event;
            event = bpf_ringbuf_reserve(&inode_events, sizeof(*event), 0);
            if (event) {
                event->timestamp = bpf_ktime_get_ns();
                event->event_type = EVENT_TYPE_CREATE;
                event->pid = bpf_get_current_pid_tgid() >> 32;
                __builtin_memcpy(event->filename, filename, sizeof(event->filename));
                bpf_ringbuf_submit(event, 0);
            }
        }
    }
    if (result == ACTION_AUDIT) {
        // Log creation operation with WRITE operation type
        log_access_event(dentry, FIM_OP_WRITE, ACTION_AUDIT, NULL);

        // Send inode update event for monitored files
    }

    return 0;
}

/**
 * fim_inode_unlink - LSM hook for file deletion operations
 * Monitors and controls file deletion, sends inode update events for monitored files
 */
SEC("lsm/inode_unlink")
int BPF_PROG(fim_inode_unlink, struct inode *dir, struct dentry *dentry) {
    if (!dentry) {
        return 0;
    }

    // Check access permissions (deletion requires write permission)
    __u32 process_dev, process_inode;
    get_current_process_dev_inode(&process_dev, &process_inode);

    int result = check_file_access_by_inode(dentry, ACCESS_WRITE, process_dev, process_inode, bpf_get_current_uid_gid() & 0xFFFFFFFF);

    if (result == ACTION_BLOCK) {
        return -EACCES; // Block deletion
    }

    // Monitor mode: log deletion and send inode update event if needed
    if (result == ACTION_AUDIT) {
        // Log deletion operation with REMOVE operation type
        log_access_event(dentry, FIM_OP_REMOVE, ACTION_AUDIT, NULL);

        // Send inode update event for monitored files
        if (should_monitor_dentry(dentry)) {
            char filename[64] = {0};
            if (extract_filename_from_dentry(dentry, filename, sizeof(filename)) == 0) {
                struct simple_file_event *event;
                event = bpf_ringbuf_reserve(&inode_events, sizeof(*event), 0);
                if (event) {
                    event->timestamp = bpf_ktime_get_ns();
                    event->event_type = EVENT_TYPE_DELETE;
                    event->pid = bpf_get_current_pid_tgid() >> 32;
                    __builtin_memcpy(event->filename, filename, sizeof(event->filename));
                    bpf_ringbuf_submit(event, 0);
                }
            }
        }
    }

    return 0;
}

/**
 * fim_inode_rename - LSM hook for file rename operations
 * Monitors and controls file renaming, sends inode update events for monitored files
 */
SEC("lsm/inode_rename")
int BPF_PROG(fim_inode_rename, struct inode *old_dir, struct dentry *old_dentry,
             struct inode *new_dir, struct dentry *new_dentry) {
    if (!old_dentry || !new_dentry) return 0;

    // Check access permissions for both old and new files
    __u32 process_dev, process_inode;
    get_current_process_dev_inode(&process_dev, &process_inode);
    __u32 uid = bpf_get_current_uid_gid() & 0xFFFFFFFF;

    int old_result = check_file_access_by_inode(old_dentry, ACCESS_WRITE, process_dev, process_inode, uid);
    int new_result = check_file_access_by_inode(new_dentry, ACCESS_WRITE, process_dev, process_inode, uid);

    // Handle BLOCK actions first (highest priority)
    if (old_result == ACTION_BLOCK || new_result == ACTION_BLOCK) {
        // Log block event for the file that caused the block
        if (old_result == ACTION_BLOCK) {
            log_access_event(old_dentry, FIM_OP_WRITE, ACTION_BLOCK, NULL);
        }
        if (new_result == ACTION_BLOCK) {
            log_access_event(new_dentry, FIM_OP_WRITE, ACTION_BLOCK, NULL);
        }
        return -EACCES; // Block rename operation
    }

    // Handle AUDIT actions (log but allow)
    if (old_result == ACTION_AUDIT) {
        log_access_event(old_dentry, FIM_OP_WRITE, ACTION_AUDIT, NULL);
    }
    if (new_result == ACTION_AUDIT) {
        log_access_event(new_dentry, FIM_OP_WRITE, ACTION_AUDIT, NULL);
    }

    // Send inode update event if rename involves monitored files
    if (should_monitor_dentry(old_dentry) || should_monitor_dentry(new_dentry)) {
        char filename[64] = {0};

        // Prefer new filename, fallback to old filename if new is not monitored
        if (should_monitor_dentry(new_dentry)) {
            extract_filename_from_dentry(new_dentry, filename, sizeof(filename));
        } else {
            extract_filename_from_dentry(old_dentry, filename, sizeof(filename));
        }

        struct simple_file_event *event;
        event = bpf_ringbuf_reserve(&inode_events, sizeof(*event), 0);
        if (event) {
            event->timestamp = bpf_ktime_get_ns();
            event->event_type = EVENT_TYPE_CREATE; // Use CREATE type to trigger update
            event->pid = bpf_get_current_pid_tgid() >> 32;
            __builtin_memcpy(event->filename, filename, sizeof(event->filename));
            bpf_ringbuf_submit(event, 0);
        }
    }

    return 0;
}

/* Additional LSM hooks for comprehensive file system monitoring */

SEC("lsm/inode_mkdir")
int BPF_PROG(fim_inode_mkdir, struct inode *dir, struct dentry *dentry, umode_t mode) {
    if (!dentry) return 0;

    __u32 process_dev, process_inode;
    get_current_process_dev_inode(&process_dev, &process_inode);

    int result = check_file_access_by_inode(dentry, ACCESS_WRITE, process_dev, process_inode, bpf_get_current_uid_gid() & 0xFFFFFFFF);

    if (result == ACTION_BLOCK) {
        log_access_event(dentry, FIM_OP_WRITE, ACTION_BLOCK, NULL);
        return -EACCES;
    } else if (result == ACTION_AUDIT) {
        log_access_event(dentry, FIM_OP_WRITE, ACTION_AUDIT, NULL);
    }
    return 0;
}

SEC("lsm/inode_rmdir")
int BPF_PROG(fim_inode_rmdir, struct inode *dir, struct dentry *dentry) {
    if (!dentry) return 0;

    __u32 process_dev, process_inode;
    get_current_process_dev_inode(&process_dev, &process_inode);

    int result = check_file_access_by_inode(dentry, ACCESS_WRITE, process_dev, process_inode, bpf_get_current_uid_gid() & 0xFFFFFFFF);

    if (result == ACTION_BLOCK) {
        log_access_event(dentry, FIM_OP_WRITE, ACTION_BLOCK, NULL);
        return -EACCES;
    } else if (result == ACTION_AUDIT) {
        log_access_event(dentry, FIM_OP_WRITE, ACTION_AUDIT, NULL);
    }
    return 0;
}

SEC("lsm/inode_setattr")
int BPF_PROG(fim_inode_setattr, struct dentry *dentry, struct iattr *attr) {
    if (!dentry) return 0;

    __u32 process_dev, process_inode;
    get_current_process_dev_inode(&process_dev, &process_inode);

    int result = check_file_access_by_inode(dentry, ACCESS_WRITE, process_dev, process_inode, bpf_get_current_uid_gid() & 0xFFFFFFFF);

    if (result == ACTION_BLOCK) {
        log_access_event(dentry, FIM_OP_WRITE, ACTION_BLOCK, NULL);
        return -EACCES;
    } else if (result == ACTION_AUDIT) {
        log_access_event(dentry, FIM_OP_WRITE, ACTION_AUDIT, NULL);
    }
    return 0;
}

SEC("lsm/inode_link")
int BPF_PROG(fim_inode_link, struct dentry *old_dentry, struct inode *dir, struct dentry *new_dentry) {
    if (!new_dentry) return 0;

    __u32 process_dev, process_inode;
    get_current_process_dev_inode(&process_dev, &process_inode);

    int result = check_file_access_by_inode(new_dentry, ACCESS_WRITE, process_dev, process_inode, bpf_get_current_uid_gid() & 0xFFFFFFFF);

    if (result == ACTION_BLOCK) {
        log_access_event(new_dentry, FIM_OP_WRITE, ACTION_BLOCK, NULL);
        return -EACCES;
    } else if (result == ACTION_AUDIT) {
        log_access_event(new_dentry, FIM_OP_WRITE, ACTION_AUDIT, NULL);
    }
    return 0;
}

SEC("lsm/inode_symlink")
int BPF_PROG(fim_inode_symlink, struct inode *dir, struct dentry *dentry, const char *old_name) {
    if (!dentry) return 0;

    __u32 process_dev, process_inode;
    get_current_process_dev_inode(&process_dev, &process_inode);

    int result = check_file_access_by_inode(dentry, ACCESS_WRITE, process_dev, process_inode, bpf_get_current_uid_gid() & 0xFFFFFFFF);

    if (result == ACTION_BLOCK) {
        log_access_event(dentry, FIM_OP_WRITE, ACTION_BLOCK, NULL);
        return -EACCES;
    } else if (result == ACTION_AUDIT) {
        log_access_event(dentry, FIM_OP_WRITE, ACTION_AUDIT, NULL);
    }
    return 0;
}
