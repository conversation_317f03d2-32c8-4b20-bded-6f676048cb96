#ifndef FIM_EBPF_MANAGER_H
#define FIM_EBPF_MANAGER_H

#include "fim_common.h"

/* Forward declarations for eBPF structures */
struct file_access_key;
struct allowed_entry;
struct filesystem_allowed_entry;

/* ========== eBPF Program Management ========== */

/**
 * Load eBPF object file
 * @param obj_file: Path to eBPF object file
 * @return: 0 on success, negative error code on failure
 */
int ebpf_load_program(const char *obj_file);

/**
 * Attach all LSM programs
 * @return: 0 on success, negative error code on failure
 */
int ebpf_attach_programs(void);

/**
 * Detach all programs and cleanup resources
 */
void ebpf_cleanup(void);

/**
 * Check if eBPF programs are loaded
 * @return: 1 if loaded, 0 if not
 */
int ebpf_is_loaded(void);

/* ========== eBPF Map Management ========== */

/**
 * Initialize all eBPF map file descriptors
 * @return: 0 on success, negative error code on failure
 */
int ebpf_init_maps(void);

/**
 * Clear all eBPF maps
 * @return: 0 on success, negative error code on failure
 */
int ebpf_clear_maps(void);

/**
 * Get map file descriptor by name
 * @param map_name: Name of the map
 * @return: File descriptor or -1 on error
 */
int ebpf_get_map_fd(const char *map_name);

/* ========== Rule Management ========== */

// Legacy functions - deprecated, use new configuration format instead

/* ========== Filename Monitoring ========== */

/**
 * Populate monitored filenames map
 * @return: Number of filenames added, or negative error code
 */
int ebpf_populate_filenames_map(void);

/**
 * Add filename to monitoring map
 * @param filename: Filename to monitor
 * @return: 0 on success, negative error code on failure
 */
int ebpf_add_monitored_filename(const char *filename);

/**
 * Remove filename from monitoring map
 * @param filename: Filename to remove
 * @return: 0 on success, negative error code on failure
 */
int ebpf_remove_monitored_filename(const char *filename);

/* ========== Map Utilities ========== */

/**
 * Print map contents for debugging
 * @param map_name: Name of map to print
 */
void ebpf_print_map_contents(const char *map_name);

/**
 * Get map statistics
 * @param map_name: Name of map
 * @param entries: Output number of entries
 * @param max_entries: Output maximum entries
 * @return: 0 on success, negative error code on failure
 */
int ebpf_get_map_stats(const char *map_name, __u32 *entries, __u32 *max_entries);

/* ========== Program Information ========== */

/**
 * Get loaded program names
 * @param programs: Output array of program names
 * @param max_programs: Maximum number of programs
 * @return: Number of programs loaded
 */
int ebpf_get_loaded_programs(char programs[][64], int max_programs);

/**
 * Print eBPF program status
 */
void ebpf_print_status(void);

/* ========== New Configuration Management ========== */

/**
 * Apply new configuration format rules to eBPF maps
 * @param config: New configuration structure
 * @return: Number of rules applied, or negative error code
 */
int ebpf_apply_new_config(struct fim_new_config *config);

/**
 * Apply file_access configuration to eBPF maps
 * @param config_list: List of file_access configurations
 * @return: Number of rules applied, or negative error code
 */
int ebpf_apply_file_access_config(struct file_access_config *config_list);

/**
 * Add file_access exceptions to eBPF map
 * @param rule_key: File access rule key
 * @param allowed_list: List of allowed entries
 * @return: Number of exceptions added, or negative error code
 */
int ebpf_add_file_access_exceptions(struct file_access_key *rule_key, struct allowed_entry *allowed_list);

/**
 * Apply filesystem_protect configuration to eBPF maps
 * @param config: Filesystem protect configuration
 * @return: 0 on success, negative error code on failure
 */
int ebpf_apply_filesystem_protect_config(struct filesystem_protect_config *config);

/**
 * Add filesystem_protect exceptions to eBPF map
 * @param allowed_list: List of filesystem allowed entries
 * @return: Number of exceptions added, or negative error code
 */
int ebpf_add_filesystem_protect_exceptions(struct filesystem_allowed_entry *allowed_list);

/**
 * Populate monitored filenames map from new configuration
 * @param config: New configuration structure
 * @return: Number of filenames added, or negative error code
 */
int ebpf_populate_new_filenames_map(struct fim_new_config *config);

/**
 * Clear new configuration maps
 */
void ebpf_clear_new_config_maps(void);

#endif /* FIM_EBPF_MANAGER_H */
