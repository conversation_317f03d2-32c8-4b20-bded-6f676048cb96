# FIM - File Integrity Monitoring

A high-performance, eBPF-based file integrity monitoring system that provides real-time file access control and monitoring using Linux Security Module (LSM) hooks.

## Overview

FIM is a modern file integrity monitoring solution that leverages eBPF (Extended Berkeley Packet Filter) technology to provide:

- **Real-time file access monitoring** - Monitor file read, write, create, delete, and rename operations
- **Granular access control** - Block or audit file operations based on configurable rules
- **Process-aware security** - Control access based on process executable and user identity
- **High performance** - eBPF-based implementation with minimal system overhead
- **Flexible configuration** - YAML-based configuration with support for exceptions and wildcards

## Key Features

### eBPF-Based LSM Hooks
- Utilizes Linux Security Module hooks for kernel-level file access interception
- Supports multiple LSM hook points: `file_open`, `inode_create`, `inode_unlink`, `inode_rename`, etc.
- Real-time event processing with ring buffer communication

### File Access Control
- **Read/Write Rules** - Separate control for read and write operations
- **Action Types** - Block (deny access) or Audit (log but allow) modes
- **Exception Handling** - Allow specific processes or users to bypass rules
- **Directory Support** - Apply rules to directories and their contents

### Filesystem Protection
- **Global Read-Only Protection** - Default deny-all with explicit allow lists
- **Process-Based Exceptions** - Allow specific processes to write to protected areas
- **User-Based Exceptions** - Grant write permissions to specific users

### Advanced Features
- **Inode-Based Tracking** - Efficient file identification using inode numbers
- **Dynamic Inode Updates** - Automatic handling of file creation/deletion events
- **Hierarchical Rule Matching** - Check parent directories for applicable rules
- **Real-Time Logging** - Structured event logging with timestamps and process information

## System Requirements

### Kernel Requirements
- Linux kernel 5.8+ with eBPF support
- LSM (Linux Security Modules) enabled
- BPF LSM support (`CONFIG_BPF_LSM=y`)

### Dependencies
- `libbpf` - eBPF library
- `libelf` - ELF file handling
- `libyaml` - YAML configuration parsing
- `clang` - LLVM compiler for eBPF programs
- `llvm` - LLVM tools for eBPF

### Build Tools
- `make` - Build system
- `clang` - C compiler
- `llvm-strip` - Binary stripping tool

## Quick Start

### 1. Clone and Build
```bash
git clone <repository-url>
cd FIM
make
```

### 2. Basic Configuration
Create a configuration file or use the provided example:
```bash
sudo cp examples/configs/basic_example.yaml /etc/fim/policy.yaml
```

### 3. Test Configuration
```bash
sudo ./bin/fim-ctl test /etc/fim/policy.yaml
```

### 4. Start Monitoring
```bash
sudo ./bin/fim-ctl start /etc/fim/policy.yaml
```

## Basic Usage Examples

### File Access Control
```yaml
file_access:
  - rule_id: "protect_sensitive_files"
    enabled: true
    rules:
      - path: "/etc/passwd"
        access: "write"
        action: "block"
        allowed:
          - process: "/usr/bin/passwd"
            uid: 0
```

### Directory Protection
```yaml
file_access:
  - rule_id: "protect_config_dir"
    enabled: true
    rules:
      - path: "/etc/myapp"
        access: "write"
        action: "audit"
        dir: true
        allowed:
          - process: "*"
            uid: 1000
```

### Global Filesystem Protection
```yaml
filesystem_protect:
  - rule_id: "global_readonly"
    enabled: true
    action: "block"
    allowed:
      - path: "/tmp"
        process_path: "*"
        uid: 1000
        is_dir: true
```

## Project Structure

```
FIM/
├── src/
│   ├── ebpf/           # eBPF kernel programs
│   ├── userspace/      # User-space control programs
│   └── include/        # Shared header files
├── config/             # Default configuration files
├── examples/           # Example configurations
├── scripts/            # Installation and service scripts
└── bin/                # Compiled binaries
```

## Documentation

- [INSTALL.md](INSTALL.md) - Installation and setup guide
- [CONFIGURATION.md](CONFIGURATION.md) - Configuration reference
- [API.md](API.md) - Command-line interface documentation

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

Please read our contributing guidelines before submitting pull requests.

## Support

For issues and questions, please use the GitHub issue tracker.
