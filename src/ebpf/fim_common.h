#ifndef __FIM_EBPF_COMMON_H__
#define __FIM_EBPF_COMMON_H__

#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>

/* Error codes */
#define EACCES 13

/* Operation types */
#define FIM_OP_READ   1
#define FIM_OP_WRITE  2
#define FIM_OP_REMOVE 3

/* Access types */
#define ACCESS_READ   1
#define ACCESS_WRITE  2

/* Action types */
#define ACTION_AUDIT 1  /* Audit mode: log but allow */
#define ACTION_BLOCK 2  /* Block mode: deny operation */

/* Special values */
#define UID_ANY         0xFFFFFFFF
#define PROCESS_ANY     0
#define DEVICE_ANY      0xFFFFFFFF  /* Device wildcard - use max value to avoid conflicts */
#define RULE_NOT_FOUND  -1

/* eBPF map sizes */
#define MONITORED_FILENAMES_MAX_ENTRIES 1024
#define LOG_EVENTS_RINGBUF_SIZE (1024 * 1024)  /* 1MB */
#define INODE_EVENTS_RINGBUF_SIZE (256 * 1024) /* 256KB */

/* Event types */
#define EVENT_TYPE_CREATE 1
#define EVENT_TYPE_DELETE 2
#define EVENT_TYPE_RENAME 3

/* File mode constants */
#define FMODE_READ  0x1
#define FMODE_WRITE 0x2


/* ========== Data Structures ========== */

/* File access rule key - now includes device number for unique identification */
struct file_access_key {
    __u32 dev;         /* Device number (major:minor) */
    __u32 inode;       /* File inode */
    __u32 access_type; /* ACCESS_READ or ACCESS_WRITE */
};

/* File access rule value */
struct file_access_value {
    __u32 rule_enabled;   /* Rule enabled flag */
    __u32 action;         /* ACTION_AUDIT or ACTION_BLOCK */
    __u32 has_exceptions; /* Has exception rules flag */
    char rule_id[64];     /* Rule ID for logging */
};

/* File access exception key - now includes device numbers for unique identification */
struct file_access_exception_key {
    __u32 dev;           /* File device number (major:minor) */
    __u32 inode;         /* File inode */
    __u32 access_type;   /* Access type */
    __u32 process_dev;   /* Process executable device number (0 = wildcard) */
    __u32 process_inode; /* Process executable inode (0 = wildcard) */
    __u32 uid;           /* User ID (UID_ANY = wildcard) */
};

/* File access exception value */
struct file_access_exception_value {
    __u32 allowed; /* Allow flag */
};

/* Filesystem protect configuration */
struct filesystem_protect_config {
    __u32 enabled;        /* Global protection enabled */
    __u32 default_action; /* Default action */
    char rule_id[64];     /* Rule ID */
};

/* Filesystem protect exception key - now includes device numbers for unique identification */
struct filesystem_exception_key {
    __u32 dev;           /* File/directory device number (major:minor) */
    __u32 inode;         /* File/directory inode */
    __u32 process_dev;   /* Process executable device number (0 = wildcard) */
    __u32 process_inode; /* Process executable inode (0 = wildcard) */
    __u32 uid;           /* User ID (UID_ANY = wildcard) */
};

/* Filesystem protect exception value */
struct filesystem_exception_value {
    __u32 allowed; /* Allow write flag */
    __u32 is_dir;  /* Directory flag */
};


/* Log event structure - timestamp added in userspace */
struct fim_log_event {
    __u32 pid;              /* Process ID */
    __u32 uid;              /* User ID */
    __u32 operation;        /* Operation type (FIM_OP_*) */
    __u32 action;           /* Rule action (ACTION_*) */
    __u32 dev;              /* File device number (major:minor) */
    __u32 inode;            /* File inode */
    char comm[16];          /* Process name */
    char target_path[256];  /* Target file path */
};

/* Simple file event structure for inode changes */
struct simple_file_event {
    __u64 timestamp;        /* Event timestamp */
    __u32 event_type;       /* Event type (EVENT_TYPE_*) */
    __u32 pid;              /* Process ID */
    char filename[64];      /* Filename only */
};


/* ========== eBPF Maps ========== */

/* Monitored filenames map for simple filename matching */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, char[64]);    /* Filename as key */
    __type(value, __u32);     /* Monitor flag (1 = monitored) */
    __uint(max_entries, MONITORED_FILENAMES_MAX_ENTRIES);
} monitored_filenames_map SEC(".maps");

/* Log events ring buffer */
struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, LOG_EVENTS_RINGBUF_SIZE);
} log_events SEC(".maps");

/* Inode change events ring buffer */
struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, INODE_EVENTS_RINGBUF_SIZE);
} inode_events SEC(".maps");

/* File access rules map */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, struct file_access_key);
    __type(value, struct file_access_value);
    __uint(max_entries, 1024);
} file_access_map SEC(".maps");

/* File access exceptions map */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, struct file_access_exception_key);
    __type(value, struct file_access_exception_value);
    __uint(max_entries, 4096);
} file_access_exc SEC(".maps");

/* Filesystem protect configuration */
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __type(key, __u32);
    __type(value, struct filesystem_protect_config);
    __uint(max_entries, 1);
} filesystem_prot SEC(".maps");

/* Filesystem protect exceptions map */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, struct filesystem_exception_key);
    __type(value, struct filesystem_exception_value);
    __uint(max_entries, 2048);
} filesystem_exce SEC(".maps");

#endif /* __FIM_EBPF_COMMON_H__ */