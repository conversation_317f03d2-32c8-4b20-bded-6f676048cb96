#include "config.h"
#include "utils.h"

/* Global configuration - defined in main.c */
extern struct fim_new_config *current_fim_config;

/* Forward declarations for new format parsing functions */
static int skip_yaml_value(yaml_parser_t *parser);

/* New format parsing functions */
static int parse_new_root_mapping(yaml_parser_t *parser);
static int parse_file_access_entry(yaml_parser_t *parser);
static int parse_file_access_rules_sequence(yaml_parser_t *parser, struct file_access_config *config);
static int parse_file_access_rule(yaml_parser_t *parser, struct file_access_config *config);
static int parse_allowed_list(yaml_parser_t *parser, struct allowed_entry **allowed_list);
static int parse_allowed_entry(yaml_parser_t *parser, struct allowed_entry **allowed_list);
static int parse_filesystem_allowed_list(yaml_parser_t *parser, struct filesystem_allowed_entry **allowed_list);

/* ========== Configuration Validation ========== */

int validate_config_file(const char *filename) {
    if (!filename) {
        FIM_LOG_ERROR("Configuration filename is NULL");
        return FIM_ERROR_INVALID_PARAM;
    }

    if (!path_exists(filename)) {
        FIM_LOG_ERROR("Configuration file does not exist: %s", filename);
        return FIM_ERROR_IO;
    }

    // Try to open and parse the YAML file
    FILE *file = fopen(filename, "r");
    if (!file) {
        FIM_LOG_ERROR("Cannot open configuration file: %s", filename);
        return FIM_ERROR_IO;
    }

    yaml_parser_t parser;
    if (!yaml_parser_initialize(&parser)) {
        FIM_LOG_ERROR("Failed to initialize YAML parser");
        fclose(file);
        return FIM_ERROR_CONFIG;
    }

    yaml_parser_set_input_file(&parser, file);

    yaml_event_t event;
    int valid = 1;

    // Basic YAML structure validation
    while (valid && yaml_parser_parse(&parser, &event)) {
        if (event.type == YAML_STREAM_END_EVENT) {
            break;
        }
        yaml_event_delete(&event);
    }

    if (parser.error != YAML_NO_ERROR) {
        FIM_LOG_ERROR("YAML parsing error in %s: %s", filename, parser.problem);
        valid = 0;
    }

    yaml_event_delete(&event);
    yaml_parser_delete(&parser);
    fclose(file);

    return valid ? FIM_SUCCESS : FIM_ERROR_CONFIG;
}

int validate_rule_path(const char *path) {
    if (!path || strlen(path) == 0) {
        return 0;
    }

    // Path should be absolute
    if (path[0] != '/') {
        FIM_LOG_ERROR("Rule path must be absolute: %s", path);
        return 0;
    }

    // Path length check
    if (strlen(path) >= MAX_PATH_LEN) {
        FIM_LOG_ERROR("Rule path too long: %s", path);
        return 0;
    }

    return 1;
}

int validate_action_string(const char *action) {
    if (!action) {
        return -1;
    }

    if (strcmp(action, "block") == 0) {
        return ACTION_BLOCK;
    } else if (strcmp(action, "monitor") == 0) {
        return ACTION_AUDIT;
    }

    FIM_LOG_ERROR("Invalid action: %s (must be 'block' or 'monitor')", action);
    return -1;
}

int validate_operation_string(const char *operation) {
    if (!operation) {
        return -1;
    }

    if (strcmp(operation, "read") == 0) {
        return FIM_OP_READ;
    } else if (strcmp(operation, "write") == 0) {
        return FIM_OP_WRITE;
    }

    FIM_LOG_ERROR("Invalid operation: %s (must be 'read' or 'write')", operation);
    return -1;
}

/* ========== Configuration Management ========== */

void clear_config(void) {
    // Clear new configuration
    if (current_fim_config) {
        free_new_config(current_fim_config);
        current_fim_config = NULL;
    }

    if (current_config_file) {
        free(current_config_file);
        current_config_file = NULL;
    }
}

const char *get_current_config_file(void) {
    return current_config_file;
}

int set_current_config_file(const char *filename) {
    if (!filename) {
        return -1;
    }

    if (current_config_file) {
        free(current_config_file);
    }

    current_config_file = strdup(filename);
    return current_config_file ? 0 : -1;
}

/* ========== Legacy YAML Configuration Parsing (Removed) ========== */
// All legacy parsing functions have been removed
// Use parse_new_yaml_config instead



/* ========== Utility Functions ========== */

static int skip_yaml_value(yaml_parser_t *parser) {
    yaml_event_t event;
    int depth = 0;

    do {
        if (!yaml_parser_parse(parser, &event)) {
            return FIM_ERROR_CONFIG;
        }

        switch (event.type) {
            case YAML_MAPPING_START_EVENT:
            case YAML_SEQUENCE_START_EVENT:
                depth++;
                break;
            case YAML_MAPPING_END_EVENT:
            case YAML_SEQUENCE_END_EVENT:
                depth--;
                break;
            default:
                break;
        }

        yaml_event_delete(&event);
    } while (depth > 0);

    return FIM_SUCCESS;
}

/* ========== New YAML Configuration Parsing ========== */

int parse_new_yaml_config(const char *filename) {
    if (!filename) {
        return FIM_ERROR_INVALID_PARAM;
    }

    FILE *file = fopen(filename, "r");
    if (!file) {
        FIM_LOG_ERROR("Cannot open config file: %s", filename);
        return FIM_ERROR_IO;
    }

    yaml_parser_t parser;
    if (!yaml_parser_initialize(&parser)) {
        FIM_LOG_ERROR("Failed to initialize YAML parser");
        fclose(file);
        return FIM_ERROR_CONFIG;
    }

    yaml_parser_set_input_file(&parser, file);

    // Free existing configuration
    if (current_fim_config) {
        free_new_config(current_fim_config);
        current_fim_config = NULL;
    }

    // Allocate new configuration
    current_fim_config = malloc(sizeof(struct fim_new_config));
    if (!current_fim_config) {
        FIM_LOG_ERROR("Failed to allocate memory for configuration");
        yaml_parser_delete(&parser);
        fclose(file);
        return FIM_ERROR_MEMORY;
    }

    memset(current_fim_config, 0, sizeof(struct fim_new_config));

    yaml_event_t event;
    int result = FIM_SUCCESS;
    int in_document = 0;

    FIM_LOG_INFO("Parsing new YAML config: %s", filename);

    while (result == FIM_SUCCESS) {
        if (!yaml_parser_parse(&parser, &event)) {
            FIM_LOG_ERROR("YAML parser error: %s", parser.problem);
            result = FIM_ERROR_CONFIG;
            break;
        }

        switch (event.type) {
            case YAML_STREAM_START_EVENT:
                break;

            case YAML_DOCUMENT_START_EVENT:
                in_document = 1;
                break;

            case YAML_MAPPING_START_EVENT:
                if (in_document) {
                    result = parse_new_root_mapping(&parser);
                }
                break;

            case YAML_DOCUMENT_END_EVENT:
                in_document = 0;
                break;

            case YAML_STREAM_END_EVENT:
                goto cleanup;

            default:
                break;
        }

        yaml_event_delete(&event);
    }

cleanup:
    yaml_event_delete(&event);
    yaml_parser_delete(&parser);
    fclose(file);

    if (result == FIM_SUCCESS) {
        FIM_LOG_INFO("Successfully parsed new configuration format");
        set_current_config_file(filename);

        // Validate configuration
        result = validate_new_config(current_fim_config);
        if (result == FIM_SUCCESS) {
            print_new_config_summary(current_fim_config);
        }
    } else {
        // Clean up on error
        if (current_fim_config) {
            free_new_config(current_fim_config);
            current_fim_config = NULL;
        }
    }

    return result;
}

static int parse_new_root_mapping(yaml_parser_t *parser) {
    yaml_event_t event;
    char key[MAX_YAML_VALUE_LEN];
    int result = FIM_SUCCESS;

    while (result == FIM_SUCCESS) {
        if (!yaml_parser_parse(parser, &event)) {
            return FIM_ERROR_CONFIG;
        }

        if (event.type == YAML_MAPPING_END_EVENT) {
            yaml_event_delete(&event);
            break;
        }

        if (event.type == YAML_SCALAR_EVENT) {
            safe_strncpy(key, (char*)event.data.scalar.value, sizeof(key));
            yaml_event_delete(&event);

            if (strcmp(key, "file_access") == 0) {
                result = parse_file_access_section(parser);
            } else if (strcmp(key, "filesystem_protect") == 0) {
                result = parse_filesystem_protect_section(parser);
            } else {
                // Skip unknown keys
                result = skip_yaml_value(parser);
            }
        } else {
            yaml_event_delete(&event);
        }
    }

    return result;
}

int parse_file_access_section(yaml_parser_t *parser) {
    yaml_event_t event;
    int result = FIM_SUCCESS;

    // Expect sequence start
    if (!yaml_parser_parse(parser, &event)) {
        return FIM_ERROR_CONFIG;
    }

    if (event.type != YAML_SEQUENCE_START_EVENT) {
        yaml_event_delete(&event);
        return FIM_ERROR_CONFIG;
    }
    yaml_event_delete(&event);

    // Parse sequence items
    while (result == FIM_SUCCESS) {
        if (!yaml_parser_parse(parser, &event)) {
            return FIM_ERROR_CONFIG;
        }

        if (event.type == YAML_SEQUENCE_END_EVENT) {
            yaml_event_delete(&event);
            break;
        }

        if (event.type == YAML_MAPPING_START_EVENT) {
            yaml_event_delete(&event);
            result = parse_file_access_entry(parser);
        } else {
            yaml_event_delete(&event);
        }
    }

    return result;
}

static int parse_file_access_entry(yaml_parser_t *parser) {
    yaml_event_t event;
    char key[MAX_YAML_VALUE_LEN];
    int result = FIM_SUCCESS;

    // Create new file_access_config
    struct file_access_config *config = malloc(sizeof(struct file_access_config));
    if (!config) {
        FIM_LOG_ERROR("Failed to allocate memory for file_access_config");
        return FIM_ERROR_MEMORY;
    }
    memset(config, 0, sizeof(struct file_access_config));

    // Parse file_access entry mapping
    while (result == FIM_SUCCESS) {
        if (!yaml_parser_parse(parser, &event)) {
            result = FIM_ERROR_CONFIG;
            break;
        }

        if (event.type == YAML_MAPPING_END_EVENT) {
            yaml_event_delete(&event);
            break;
        }

        if (event.type == YAML_SCALAR_EVENT) {
            safe_strncpy(key, (char*)event.data.scalar.value, sizeof(key));
            yaml_event_delete(&event);

            // Parse value
            if (!yaml_parser_parse(parser, &event)) {
                result = FIM_ERROR_CONFIG;
                break;
            }

            if (strcmp(key, "enabled") == 0) {
                if (event.type == YAML_SCALAR_EVENT) {
                    config->enabled = (strcmp((char*)event.data.scalar.value, "true") == 0) ? 1 : 0;
                }
            } else if (strcmp(key, "rule_id") == 0) {
                if (event.type == YAML_SCALAR_EVENT) {
                    config->rule_id = strdup((char*)event.data.scalar.value);
                }
            } else if (strcmp(key, "rules") == 0) {
                if (event.type == YAML_SEQUENCE_START_EVENT) {
                    yaml_event_delete(&event);
                    result = parse_file_access_rules_sequence(parser, config);
                    continue; // Skip yaml_event_delete at the end
                }
            }

            yaml_event_delete(&event);
        } else {
            yaml_event_delete(&event);
        }
    }

    if (result == FIM_SUCCESS) {
        // Add to configuration list
        config->next = current_fim_config->file_access_list;
        current_fim_config->file_access_list = config;

        FIM_LOG_INFO("✓ Loaded file_access config: rule_id=%s, enabled=%s",
                    config->rule_id ? config->rule_id : "default",
                    config->enabled ? "true" : "false");
    } else {
        // Clean up on error
        if (config->rule_id) free(config->rule_id);
        free(config);
    }

    return result;
}

static int parse_file_access_rules_sequence(yaml_parser_t *parser, struct file_access_config *config) {
    yaml_event_t event;
    int result = FIM_SUCCESS;

    // Parse sequence items
    while (result == FIM_SUCCESS) {
        if (!yaml_parser_parse(parser, &event)) {
            return FIM_ERROR_CONFIG;
        }

        if (event.type == YAML_SEQUENCE_END_EVENT) {
            yaml_event_delete(&event);
            break;
        }

        if (event.type == YAML_MAPPING_START_EVENT) {
            yaml_event_delete(&event);
            result = parse_file_access_rule(parser, config);
        } else {
            yaml_event_delete(&event);
        }
    }

    return result;
}

static int parse_file_access_rule(yaml_parser_t *parser, struct file_access_config *config) {
    yaml_event_t event;
    char key[MAX_YAML_VALUE_LEN];
    int result = FIM_SUCCESS;

    // Create new rule
    struct file_access_rule *rule = malloc(sizeof(struct file_access_rule));
    if (!rule) {
        FIM_LOG_ERROR("Failed to allocate memory for file_access_rule");
        return FIM_ERROR_MEMORY;
    }
    memset(rule, 0, sizeof(struct file_access_rule));

    // Parse rule mapping
    while (result == FIM_SUCCESS) {
        if (!yaml_parser_parse(parser, &event)) {
            result = FIM_ERROR_CONFIG;
            break;
        }

        if (event.type == YAML_MAPPING_END_EVENT) {
            yaml_event_delete(&event);
            break;
        }

        if (event.type == YAML_SCALAR_EVENT) {
            safe_strncpy(key, (char*)event.data.scalar.value, sizeof(key));
            yaml_event_delete(&event);

            // Parse value
            if (!yaml_parser_parse(parser, &event)) {
                result = FIM_ERROR_CONFIG;
                break;
            }

            if (strcmp(key, "path") == 0) {
                if (event.type == YAML_SCALAR_EVENT) {
                    rule->path = strdup((char*)event.data.scalar.value);
                }
            } else if (strcmp(key, "sub_rule_id") == 0) {
                if (event.type == YAML_SCALAR_EVENT) {
                    rule->sub_rule_id = strdup((char*)event.data.scalar.value);
                }
            } else if (strcmp(key, "access") == 0) {
                if (event.type == YAML_SCALAR_EVENT) {
                    rule->access_type = parse_access_type((char*)event.data.scalar.value);
                    if (rule->access_type < 0) {
                        FIM_LOG_ERROR("Invalid access type: %s", (char*)event.data.scalar.value);
                        result = FIM_ERROR_CONFIG;
                    }
                }
            } else if (strcmp(key, "dir") == 0) {
                if (event.type == YAML_SCALAR_EVENT) {
                    rule->is_dir = (strcmp((char*)event.data.scalar.value, "true") == 0) ? 1 : 0;
                }
            } else if (strcmp(key, "action") == 0) {
                if (event.type == YAML_SCALAR_EVENT) {
                    rule->action = parse_action_type((char*)event.data.scalar.value);
                    if (rule->action < 0) {
                        FIM_LOG_ERROR("Invalid action type: %s", (char*)event.data.scalar.value);
                        result = FIM_ERROR_CONFIG;
                    }
                }
            } else if (strcmp(key, "allowed") == 0) {
                if (event.type == YAML_SEQUENCE_START_EVENT) {
                    yaml_event_delete(&event);
                    result = parse_allowed_list(parser, &rule->allowed_list);
                    continue; // Skip yaml_event_delete at the end
                }
            }

            yaml_event_delete(&event);
        } else {
            yaml_event_delete(&event);
        }
    }

    if (result == FIM_SUCCESS && rule->path) {
        // Add to rule list
        rule->next = config->rules;
        config->rules = rule;

        FIM_LOG_INFO("  ✓ Added file_access rule: path=%s, access=%s, action=%s",
                    rule->path,
                    rule->access_type == ACCESS_READ ? "read" : "write",
                    rule->action == ACTION_BLOCK ? "block" : "audit");
    } else {
        // Clean up on error
        if (rule->path) free(rule->path);
        if (rule->sub_rule_id) free(rule->sub_rule_id);
        free(rule);
    }

    return result;
}

static int parse_allowed_list(yaml_parser_t *parser, struct allowed_entry **allowed_list) {
    yaml_event_t event;
    int result = FIM_SUCCESS;

    // Parse sequence items
    while (result == FIM_SUCCESS) {
        if (!yaml_parser_parse(parser, &event)) {
            return FIM_ERROR_CONFIG;
        }

        if (event.type == YAML_SEQUENCE_END_EVENT) {
            yaml_event_delete(&event);
            break;
        }

        if (event.type == YAML_MAPPING_START_EVENT) {
            yaml_event_delete(&event);
            result = parse_allowed_entry(parser, allowed_list);
        } else {
            yaml_event_delete(&event);
        }
    }

    return result;
}

static int parse_allowed_entry(yaml_parser_t *parser, struct allowed_entry **allowed_list) {
    yaml_event_t event;
    char key[MAX_YAML_VALUE_LEN];
    int result = FIM_SUCCESS;

    // Create new allowed entry
    struct allowed_entry *entry = malloc(sizeof(struct allowed_entry));
    if (!entry) {
        FIM_LOG_ERROR("Failed to allocate memory for allowed_entry");
        return FIM_ERROR_MEMORY;
    }
    memset(entry, 0, sizeof(struct allowed_entry));
    entry->uid = UID_ANY; // Default to any UID

    // Parse allowed entry mapping
    while (result == FIM_SUCCESS) {
        if (!yaml_parser_parse(parser, &event)) {
            result = FIM_ERROR_CONFIG;
            break;
        }

        if (event.type == YAML_MAPPING_END_EVENT) {
            yaml_event_delete(&event);
            break;
        }

        if (event.type == YAML_SCALAR_EVENT) {
            safe_strncpy(key, (char*)event.data.scalar.value, sizeof(key));
            yaml_event_delete(&event);

            // Parse value
            if (!yaml_parser_parse(parser, &event)) {
                result = FIM_ERROR_CONFIG;
                break;
            }

            if (strcmp(key, "process") == 0) {
                if (event.type == YAML_SCALAR_EVENT) {
                    entry->process_path = strdup((char*)event.data.scalar.value);
                }
            } else if (strcmp(key, "uid") == 0) {
                if (event.type == YAML_SCALAR_EVENT) {
                    entry->uid = (__u32)atoi((char*)event.data.scalar.value);
                }
            }

            yaml_event_delete(&event);
        } else {
            yaml_event_delete(&event);
        }
    }

    if (result == FIM_SUCCESS) {
        // Add to allowed list
        entry->next = *allowed_list;
        *allowed_list = entry;


    } else {
        // Clean up on error
        if (entry->process_path) free(entry->process_path);
        free(entry);
    }

    return result;
}

/* ========== Utility Functions ========== */

int parse_access_type(const char *access_str) {
    if (!access_str) {
        return -1;
    }

    if (strcmp(access_str, "read") == 0) {
        return ACCESS_READ;
    } else if (strcmp(access_str, "write") == 0) {
        return ACCESS_WRITE;
    }

    return -1;
}

int parse_action_type(const char *action_str) {
    if (!action_str) {
        return -1;
    }

    if (strcmp(action_str, "block") == 0) {
        return ACTION_BLOCK;
    } else if (strcmp(action_str, "audit") == 0) {
        return ACTION_AUDIT;
    }

    return -1;
}

/* ========== Filesystem Protect Parsing ========== */

int parse_filesystem_protect_section(yaml_parser_t *parser) {
    yaml_event_t event;
    char key[MAX_YAML_VALUE_LEN];
    int result = FIM_SUCCESS;

    // Expect sequence start
    if (!yaml_parser_parse(parser, &event)) {
        return FIM_ERROR_CONFIG;
    }

    if (event.type != YAML_SEQUENCE_START_EVENT) {
        yaml_event_delete(&event);
        return FIM_ERROR_CONFIG;
    }
    yaml_event_delete(&event);

    // Parse sequence items (should be only one)
    if (!yaml_parser_parse(parser, &event)) {
        return FIM_ERROR_CONFIG;
    }

    if (event.type == YAML_MAPPING_START_EVENT) {
        yaml_event_delete(&event);

        // Allocate filesystem protect config
        current_fim_config->filesystem_protect = malloc(sizeof(struct filesystem_protect_config));
        if (!current_fim_config->filesystem_protect) {
            FIM_LOG_ERROR("Failed to allocate memory for filesystem_protect_config");
            return FIM_ERROR_MEMORY;
        }
        memset(current_fim_config->filesystem_protect, 0, sizeof(struct filesystem_protect_config));

        // Parse filesystem protect mapping
        while (result == FIM_SUCCESS) {
            if (!yaml_parser_parse(parser, &event)) {
                result = FIM_ERROR_CONFIG;
                break;
            }

            if (event.type == YAML_MAPPING_END_EVENT) {
                yaml_event_delete(&event);
                break;
            }

            if (event.type == YAML_SCALAR_EVENT) {
                safe_strncpy(key, (char*)event.data.scalar.value, sizeof(key));
                yaml_event_delete(&event);

                // Parse value
                if (!yaml_parser_parse(parser, &event)) {
                    result = FIM_ERROR_CONFIG;
                    break;
                }

                if (strcmp(key, "enable") == 0) {
                    if (event.type == YAML_SCALAR_EVENT) {
                        current_fim_config->filesystem_protect->enabled =
                            (strcmp((char*)event.data.scalar.value, "true") == 0) ? 1 : 0;
                    }
                } else if (strcmp(key, "rule_id") == 0) {
                    if (event.type == YAML_SCALAR_EVENT) {
                        current_fim_config->filesystem_protect->rule_id =
                            strdup((char*)event.data.scalar.value);
                    }
                } else if (strcmp(key, "action") == 0) {
                    if (event.type == YAML_SCALAR_EVENT) {
                        current_fim_config->filesystem_protect->action =
                            parse_action_type((char*)event.data.scalar.value);
                        if (current_fim_config->filesystem_protect->action < 0) {
                            FIM_LOG_ERROR("Invalid action type: %s", (char*)event.data.scalar.value);
                            result = FIM_ERROR_CONFIG;
                        }
                    }
                } else if (strcmp(key, "allowed") == 0) {
                    if (event.type == YAML_SEQUENCE_START_EVENT) {
                        yaml_event_delete(&event);
                        result = parse_filesystem_allowed_list(parser,
                                    &current_fim_config->filesystem_protect->allowed_list);
                        continue; // Skip yaml_event_delete at the end
                    }
                }

                yaml_event_delete(&event);
            } else {
                yaml_event_delete(&event);
            }
        }

        if (result == FIM_SUCCESS) {
            FIM_LOG_INFO("✓ Loaded filesystem_protect config: rule_id=%s, enabled=%s, action=%s",
                        current_fim_config->filesystem_protect->rule_id ?
                            current_fim_config->filesystem_protect->rule_id : "default",
                        current_fim_config->filesystem_protect->enabled ? "true" : "false",
                        current_fim_config->filesystem_protect->action == ACTION_BLOCK ? "block" : "audit");
        }
    }

    // Skip remaining sequence items
    while (result == FIM_SUCCESS) {
        if (!yaml_parser_parse(parser, &event)) {
            return FIM_ERROR_CONFIG;
        }

        if (event.type == YAML_SEQUENCE_END_EVENT) {
            yaml_event_delete(&event);
            break;
        }

        yaml_event_delete(&event);
    }

    return result;
}

static int parse_filesystem_allowed_list(yaml_parser_t *parser, struct filesystem_allowed_entry **allowed_list) {
    // Implementation similar to parse_allowed_list but for filesystem_allowed_entry
    // For now, return success - will implement if needed
    return skip_yaml_value(parser);
}

/* ========== New Configuration Management ========== */

void free_new_config(struct fim_new_config *config) {
    if (!config) {
        return;
    }

    // Free file_access configurations
    struct file_access_config *fa_config = config->file_access_list;
    while (fa_config) {
        struct file_access_config *next_fa = fa_config->next;

        if (fa_config->rule_id) free(fa_config->rule_id);

        // Free rules
        struct file_access_rule *rule = fa_config->rules;
        while (rule) {
            struct file_access_rule *next_rule = rule->next;

            if (rule->path) free(rule->path);
            if (rule->sub_rule_id) free(rule->sub_rule_id);

            // Free allowed list
            struct allowed_entry *allowed = rule->allowed_list;
            while (allowed) {
                struct allowed_entry *next_allowed = allowed->next;
                if (allowed->process_path) free(allowed->process_path);
                free(allowed);
                allowed = next_allowed;
            }

            free(rule);
            rule = next_rule;
        }

        free(fa_config);
        fa_config = next_fa;
    }

    // Free filesystem_protect configuration
    if (config->filesystem_protect) {
        if (config->filesystem_protect->rule_id) {
            free(config->filesystem_protect->rule_id);
        }

        // Free allowed list
        struct filesystem_allowed_entry *fs_allowed = config->filesystem_protect->allowed_list;
        while (fs_allowed) {
            struct filesystem_allowed_entry *next_fs = fs_allowed->next;
            if (fs_allowed->path) free(fs_allowed->path);
            if (fs_allowed->process_path) free(fs_allowed->process_path);
            free(fs_allowed);
            fs_allowed = next_fs;
        }

        free(config->filesystem_protect);
    }

    free(config);
}

int validate_new_config(struct fim_new_config *config) {
    if (!config) {
        FIM_LOG_ERROR("Configuration is NULL");
        return FIM_ERROR_INVALID_PARAM;
    }

    int rule_count = 0;

    // Validate file_access configurations
    struct file_access_config *fa_config = config->file_access_list;
    while (fa_config) {
        if (!fa_config->enabled) {
            fa_config = fa_config->next;
            continue;
        }

        struct file_access_rule *rule = fa_config->rules;
        while (rule) {
            if (!rule->path || !validate_rule_path(rule->path)) {
                FIM_LOG_ERROR("Invalid rule path: %s", rule->path ? rule->path : "NULL");
                return FIM_ERROR_CONFIG;
            }

            if (rule->access_type != ACCESS_READ && rule->access_type != ACCESS_WRITE) {
                FIM_LOG_ERROR("Invalid access type for rule: %s", rule->path);
                return FIM_ERROR_CONFIG;
            }

            if (rule->action != ACTION_BLOCK && rule->action != ACTION_AUDIT) {
                FIM_LOG_ERROR("Invalid action for rule: %s", rule->path);
                return FIM_ERROR_CONFIG;
            }

            rule_count++;
            rule = rule->next;
        }

        fa_config = fa_config->next;
    }

    // Validate filesystem_protect configuration
    if (config->filesystem_protect && config->filesystem_protect->enabled) {
        if (config->filesystem_protect->action != ACTION_BLOCK &&
            config->filesystem_protect->action != ACTION_AUDIT) {
            FIM_LOG_ERROR("Invalid filesystem_protect action");
            return FIM_ERROR_CONFIG;
        }
    }

    FIM_LOG_INFO("Configuration validation passed: %d rules", rule_count);
    return FIM_SUCCESS;
}

void print_new_config_summary(struct fim_new_config *config) {
    if (!config) {
        return;
    }

    int file_access_rules = 0;
    int read_rules = 0, write_rules = 0;
    int block_rules = 0, audit_rules = 0;

    // Count file_access rules
    struct file_access_config *fa_config = config->file_access_list;
    while (fa_config) {
        if (fa_config->enabled) {
            struct file_access_rule *rule = fa_config->rules;
            while (rule) {
                file_access_rules++;
                if (rule->access_type == ACCESS_READ) read_rules++;
                if (rule->access_type == ACCESS_WRITE) write_rules++;
                if (rule->action == ACTION_BLOCK) block_rules++;
                if (rule->action == ACTION_AUDIT) audit_rules++;
                rule = rule->next;
            }
        }
        fa_config = fa_config->next;
    }

    printf("\n=== New Configuration Summary ===\n");
    printf("File Access Rules: %d\n", file_access_rules);
    printf("  Read rules:      %d\n", read_rules);
    printf("  Write rules:     %d\n", write_rules);
    printf("  Block actions:   %d\n", block_rules);
    printf("  Audit actions:   %d\n", audit_rules);

    if (config->filesystem_protect && config->filesystem_protect->enabled) {
        printf("Filesystem Protect: Enabled (%s)\n",
               config->filesystem_protect->action == ACTION_BLOCK ? "block" : "audit");
    } else {
        printf("Filesystem Protect: Disabled\n");
    }
    printf("=================================\n\n");
}
