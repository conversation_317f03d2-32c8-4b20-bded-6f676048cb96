# FIM Policy Configuration
# File access control policy configuration file
#
# This file defines YAML-based file access control policies
# with read/write operation separation

file:
  enable: true
  rule_id: "fim_default_policy"

  # Read operation rules
  read_rules:
    sub_rule_id: "read_rules_001"

    # Deny list for read operations
    deny_list:
      enable: true
      rules:
      # Block reading sensitive files
      - path: "/home/<USER>/test"
        dir: true
        action: "block"
        grad_rule_id: "read_deny_test_dir"
        excepts:
          - process: ["/usr/bin/cat", "/bin/ls", "/usr/bin/backup-script"]
          - user: ["backup", "monitoring"]
      - path: "/etc/shadow"
        dir: false
        action: "block"
        grad_rule_id: "read_deny_shadow"
        excepts:
          - process: ["/usr/bin/passwd", "/usr/sbin/chpasswd", "/usr/sbin/usermod"]
      - path: "/root"
        dir: true
        action: "block"
        grad_rule_id: "read_deny_root_dir"
        excepts:
          - process: ["/usr/bin/systemd", "/usr/sbin/cron"]

  # Write operation rules
  write_rules:
    sub_rule_id: "write_rules_001"

    # Deny list for write operations
    deny_list:
      enable: true
      rules:
      # Block writing to sensitive locations
      - path: "/home/<USER>/test"
        dir: true
        action: "block"
        sub_rule_id: "write_deny_test_dir"
        excepts:
          - process: ["/usr/bin/backup-script", "/usr/bin/rsync"]
          - user: ["backup"]
      - path: "/etc/passwd"
        dir: false
        action: "block"
        sub_rule_id: "write_deny_passwd"
        excepts:
          - process: ["/usr/sbin/useradd", "/usr/sbin/userdel", "/usr/sbin/usermod", "/usr/bin/passwd"]
      - path: "/etc/shadow"
        dir: false
        action: "block"
        sub_rule_id: "write_deny_shadow"
        excepts:
          - process: ["/usr/bin/passwd", "/usr/sbin/chpasswd", "/usr/sbin/usermod", "/usr/sbin/useradd"]
      - path: "/etc/sudoers"
        dir: false
        action: "block"
        sub_rule_id: "write_deny_sudoers"
        excepts:
          - process: ["/usr/sbin/visudo"]
      - path: "/boot"
        dir: true
        action: "block"
        sub_rule_id: "write_deny_boot"
        excepts:
          - process: ["/usr/sbin/grub-install", "/usr/sbin/update-grub"]
      - path: "/root"
        dir: true
        action: "block"
        sub_rule_id: "write_deny_root_dir"
        excepts:
          - process: ["/usr/bin/systemd", "/usr/sbin/cron", "/usr/sbin/logrotate"]
      - path: "/etc/ssh"
        dir: true
        action: "block"
        sub_rule_id: "write_deny_ssh_config"
        excepts:
          - process: ["/usr/sbin/sshd", "/usr/bin/ssh-keygen"]
      - path: "/usr/bin"
        dir: true
        action: "block"
        sub_rule_id: "write_deny_usr_bin"
        excepts:
          - process: ["/usr/bin/apt", "/usr/bin/yum", "/usr/bin/dnf"]
      - path: "/usr/sbin"
        dir: true
        action: "block"
        sub_rule_id: "write_deny_usr_sbin"
        excepts:
          - process: ["/usr/bin/apt", "/usr/bin/yum", "/usr/bin/dnf"]

    # Allow list for write operations (currently disabled)
    allow_list:
      enable: false
      action: "audit"
      rules:
      # Example allowed paths (not currently used)
      - path: "/tmp"
        dir: true
        grad_rule_id: "write_allow_tmp"
      - path: "/var/log"
        dir: true
        grad_rule_id: "write_allow_var_log"

# Policy metadata
metadata:
  version: "2.0"
  created: "2024-01-01"
  updated: "2024-07-08"
  author: "Security Team"
  environment: "production"
  description: "FIM policy with read/write separation for enhanced security"

  # Current implementation status
  implementation:
    read_write_separation: true
    inode_based_filtering: true
    block_action: true
    audit_action: false  # planned feature
    allow_list_support: false  # planned feature

