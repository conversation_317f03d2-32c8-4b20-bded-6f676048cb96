#ifndef FIM_CONFIG_H
#define FIM_CONFIG_H

#include "fim_common.h"

/* ========== Configuration Constants ========== */
#define MAX_YAML_VALUE_LEN 512
#define MAX_EXCEPTION_PROCESSES 16
#define MAX_EXCEPTION_USERS 16

/* ========== Configuration Validation ========== */

/**
 * Validate configuration file format
 * @param filename: Path to YAML configuration file
 * @return: 0 on success, negative error code on failure
 */
int validate_config_file(const char *filename);

/**
 * Validate a single rule path
 * @param path: File path to validate
 * @return: 1 if valid, 0 if invalid
 */
int validate_rule_path(const char *path);

/**
 * Validate action string
 * @param action: Action string ("block" or "monitor")
 * @return: Action code or -1 if invalid
 */
int validate_action_string(const char *action);

/**
 * Validate operation string
 * @param operation: Operation string ("read" or "write")
 * @return: Operation code or -1 if invalid
 */
int validate_operation_string(const char *operation);

/* ========== Configuration Parsing ========== */

/**
 * Parse YAML configuration file (legacy format)
 * @param filename: Path to YAML configuration file
 * @return: 0 on success, negative error code on failure
 */
int parse_yaml_config(const char *filename);

/**
 * Parse new YAML configuration file format
 * @param filename: Path to YAML configuration file
 * @return: 0 on success, negative error code on failure
 */
int parse_new_yaml_config(const char *filename);

/**
 * Parse file_access section
 * @param parser: YAML parser
 * @return: 0 on success, negative error code on failure
 */
int parse_file_access_section(yaml_parser_t *parser);

/**
 * Parse filesystem_protect section
 * @param parser: YAML parser
 * @return: 0 on success, negative error code on failure
 */
int parse_filesystem_protect_section(yaml_parser_t *parser);

/**
 * Parse rule section (read_rules or write_rules)
 * @param parser: YAML parser
 * @param operation: Operation type (FIM_OP_READ or FIM_OP_WRITE)
 * @return: 0 on success, negative error code on failure
 */
int parse_rule_section(yaml_parser_t *parser, int operation);

/**
 * Parse deny/allow list
 * @param parser: YAML parser
 * @param operation: Operation type
 * @param rule_type: Rule type (0=deny, 1=allow)
 * @return: 0 on success, negative error code on failure
 */
int parse_rule_list(yaml_parser_t *parser, int operation, int rule_type);

/**
 * Parse single rule entry
 * @param parser: YAML parser
 * @param operation: Operation type
 * @param rule_type: Rule type
 * @param default_action: Default action if not specified
 * @return: 0 on success, negative error code on failure
 */
int parse_single_rule(yaml_parser_t *parser, int operation, int rule_type, int default_action);

/**
 * Parse exception configuration
 * @param parser: YAML parser
 * @param path: Rule path for exceptions
 * @param operation: Operation type
 * @return: 0 on success, negative error code on failure
 */
int parse_exceptions(yaml_parser_t *parser, const char *path, int operation);

/* ========== Configuration Testing ========== */

/**
 * Test configuration file without loading
 * @param config_file: Path to configuration file
 * @return: 0 on success, negative error code on failure
 */
int test_config(const char *config_file);

/**
 * Print configuration summary
 */
void print_config_summary(void);

/**
 * Print rule statistics
 */
void print_rule_statistics(void);

/* ========== Configuration Management ========== */

/**
 * Clear all loaded configuration
 */
void clear_config(void);

/**
 * Reload configuration from file
 * @param filename: Path to configuration file
 * @return: 0 on success, negative error code on failure
 */
int reload_config(const char *filename);

/**
 * Get current configuration file path
 * @return: Path to current config file or NULL
 */
const char *get_current_config_file(void);

/**
 * Set current configuration file path
 * @param filename: Path to configuration file
 * @return: 0 on success, -1 on error
 */
int set_current_config_file(const char *filename);

/* ========== New Configuration Management ========== */

/**
 * Free new configuration structure
 * @param config: Configuration to free
 */
void free_new_config(struct fim_new_config *config);

/**
 * Validate new configuration format
 * @param config: Configuration to validate
 * @return: 0 on success, negative error code on failure
 */
int validate_new_config(struct fim_new_config *config);

/**
 * Print new configuration summary
 * @param config: Configuration to print
 */
void print_new_config_summary(struct fim_new_config *config);

/**
 * Convert access string to access type
 * @param access_str: Access string ("read" or "write")
 * @return: Access type or -1 if invalid
 */
int parse_access_type(const char *access_str);

/**
 * Convert action string to action type
 * @param action_str: Action string ("block" or "audit")
 * @return: Action type or -1 if invalid
 */
int parse_action_type(const char *action_str);

#endif /* FIM_CONFIG_H */
