#ifndef FIM_EVENT_HANDLER_H
#define FIM_EVENT_HANDLER_H

#include "fim_common.h"

/* ========== Event Processing ========== */

/**
 * Initialize event handling system
 * @return: 0 on success, negative error code on failure
 */
int event_handler_init(void);

/**
 * Cleanup event handling system
 */
void event_handler_cleanup(void);

/**
 * Start event processing loop
 * @return: 0 on success, negative error code on failure
 */
int event_handler_start(void);

/**
 * Stop event processing
 */
void event_handler_stop(void);

/* ========== Log Event Handling ========== */

/**
 * Handle log event from eBPF
 * @param ctx: Context (unused)
 * @param data: Event data
 * @param data_sz: Size of event data
 * @return: 0 on success, negative error code on failure
 */
int handle_log_event(void *ctx, void *data, size_t data_sz);

/**
 * Process and format log event
 * @param event: Log event structure
 */
void process_log_event(struct fim_log_event *event);

/* ========== Inode Event Handling ========== */

/**
 * Handle inode change event from eBPF
 * @param ctx: Context (unused)
 * @param data: Event data
 * @param data_sz: Size of event data
 * @return: 0 on success, negative error code on failure
 */
int handle_inode_event(void *ctx, void *data, size_t data_sz);

/**
 * Process inode change event
 * @param event: Inode event structure
 */
void process_inode_event(struct simple_file_event *event);

/* ========== Logging System ========== */

/**
 * Initialize logging system
 * @return: 0 on success, negative error code on failure
 */
int logger_init(void);

/**
 * Cleanup logging system
 */
void logger_cleanup(void);

/**
 * Log event to file
 * @param event: Event to log
 */
void log_event_to_file(struct fim_log_event *event);

/**
 * Log event to console
 * @param event: Event to log
 */
void log_event_to_console(struct fim_log_event *event);

/**
 * Set log level
 * @param level: Log level (0=ERROR, 1=WARN, 2=INFO, 3=DEBUG)
 */
void set_log_level(int level);

/**
 * Get current log level
 * @return: Current log level
 */
int get_log_level(void);

/* ========== Event Statistics ========== */

/**
 * Get event statistics
 * @param total_events: Output total events processed
 * @param blocked_events: Output blocked events
 * @param monitored_events: Output monitored events
 */
void get_event_statistics(__u64 *total_events, __u64 *blocked_events, __u64 *monitored_events);

/**
 * Reset event statistics
 */
void reset_event_statistics(void);

/**
 * Print event statistics
 */
void print_event_statistics(void);

/* ========== Event Filtering ========== */

/**
 * Set event filter
 * @param filter_type: Type of filter ("operation", "action", "result")
 * @param filter_value: Value to filter on
 * @return: 0 on success, negative error code on failure
 */
int set_event_filter(const char *filter_type, const char *filter_value);

/**
 * Clear all event filters
 */
void clear_event_filters(void);

/**
 * Check if event passes filters
 * @param event: Event to check
 * @return: 1 if passes, 0 if filtered out
 */
int event_passes_filters(struct fim_log_event *event);

/* ========== Real-time Monitoring ========== */

/**
 * Start real-time event monitoring thread
 * @return: 0 on success, negative error code on failure
 */
int start_realtime_monitoring(void);

/**
 * Stop real-time event monitoring
 */
void stop_realtime_monitoring(void);

/**
 * Event monitoring thread function
 * @param arg: Thread argument (unused)
 * @return: Thread return value
 */
void *event_monitoring_thread(void *arg);

/* ========== Event Callbacks ========== */

/**
 * Event callback function type
 */
typedef void (*event_callback_t)(struct fim_log_event *event, void *user_data);

/**
 * Register event callback
 * @param callback: Callback function
 * @param user_data: User data to pass to callback
 * @return: Callback ID or -1 on error
 */
int register_event_callback(event_callback_t callback, void *user_data);

/**
 * Unregister event callback
 * @param callback_id: Callback ID returned by register_event_callback
 * @return: 0 on success, -1 on error
 */
int unregister_event_callback(int callback_id);

#endif /* FIM_EVENT_HANDLER_H */
