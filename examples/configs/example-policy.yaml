# FIM策略配置示例

file:
  enable: true
  rule_id: "fim_example_policy"

  read_rules:
    sub_rule_id: "read_rules_001"
    deny_list:
      enable: true
      rules:
      - path: "/etc/passwd"
        dir: false
        action: "block"
        grad_rule_id: "read_deny_passwd"
        excepts:
          - process: ["/usr/bin/id", "/usr/bin/whoami"]

      - path: "/etc/ssh"
        dir: true
        action: "block"
        grad_rule_id: "read_deny_ssh_config"
        excepts:
          - process: ["/usr/sbin/sshd"]
          - user: ["admin"]

  write_rules:
    sub_rule_id: "write_rules_001"
    deny_list:
      enable: true
      rules:
      - path: "/etc/passwd"
        dir: false
        action: "block"
        sub_rule_id: "write_deny_passwd"
        excepts:
          - process: ["/usr/sbin/useradd", "/usr/sbin/userdel"]

      - path: "/boot"
        dir: true
        action: "block"
        sub_rule_id: "write_deny_boot"
        excepts:
          - user: ["root"]
