# FIM 文件完整性监控系统 - 使用与设计文档

## 目录

1. [系统概述](#系统概述)
2. [使用指南](#使用指南)
3. [系统设计](#系统设计)
4. [故障排除](#故障排除)

## 系统概述

FIM (File Integrity Monitoring) 是基于 eBPF LSM 的文件访问控制系统，通过内核级钩子实现实时文件操作监控和访问控制。

### 核心功能

- **文件访问控制**: 基于路径、进程、用户的细粒度访问控制，支持读写分离
- **双重保护模式**: 支持阻止模式 (block) 和审计模式 (audit)
- **文件系统保护**: 全局文件系统写保护，支持例外规则
- **实时监控**: 使用 eBPF LSM 钩子进行零延迟文件操作拦截
- **事件记录**: 通过 Ring Buffer 实时记录文件访问事件
- **动态 inode 跟踪**: 自动处理文件创建/删除时的 inode 映射更新

## 使用指南

### 系统要求

- Linux 内核 ≥ 5.8 (支持 eBPF LSM)
- 启用 BPF LSM 支持
- 依赖包：`clang`, `llvm-strip`, `libbpf-dev`, `libyaml-dev`

### 编译安装

```bash
# 获取源码
git clone <repository-url>
cd FIM

# 编译
make clean && make

# 验证编译
./bin/fim-ctl --help
```

### 安装部署

```bash
# 自动安装
sudo ./scripts/install.sh

# 手动安装
sudo cp bin/fim-ctl /usr/local/bin/
sudo mkdir -p /etc/fim
sudo cp config/policy.yaml /etc/fim/
```

### 配置文件格式

FIM 使用 YAML 格式配置文件，支持 `file_access` 和 `filesystem_protect` 两种配置模式。

#### 文件访问控制配置

```yaml
file_access:
  - rule_id: "规则组标识"
    enabled: true
    rules:
      - path: "/监控路径"
        sub_rule_id: "子规则ID"
        access: "read|write"
        dir: true|false
        action: "block|audit"
        allowed:
          - process: "/进程路径"
            uid: 用户ID
          - process: "*"  # 任意进程
            uid: 4294967295  # 任意用户
```

#### 文件系统保护配置

```yaml
filesystem_protect:
  - rule_id: "全局保护规则"
    enabled: true
    action: "block|audit"
    allowed:
      - path: "/允许路径"
        process_path: "/进程路径|*"
        uid: 用户ID|4294967295
        is_dir: true|false
```

**字段说明**:
- `path`: 监控的文件或目录路径
- `access`: 访问类型，`read` 或 `write`
- `dir`: 是否为目录，`true` 表示目录，`false` 表示文件
- `action`: 动作类型
  - `block`: 阻止访问，返回权限拒绝错误
  - `audit`: 审计记录，记录事件但允许操作继续
- `allowed`: 例外列表，允许访问的进程和用户

#### 动作模式详解

**阻止模式 (block)**:
- 拦截匹配的文件操作
- 返回 `-EACCES` 错误给应用程序
- 记录阻止事件到日志
- 适用于严格的安全控制

**审计模式 (audit)**:
- 记录匹配的文件操作
- 允许操作正常执行
- 记录审计事件到日志
- 适用于监控和合规性要求

#### 配置示例

**示例1: 保护系统文件**

```yaml
file_access:
  - rule_id: "system_protection"
    enabled: true
    rules:
      - path: "/etc/passwd"
        sub_rule_id: "passwd_write"
        access: "write"
        dir: false
        action: "block"
        allowed:
          - process: "/usr/sbin/useradd"
            uid: 0
          - process: "/usr/sbin/usermod"
            uid: 0
```

**示例2: 目录访问控制**

```yaml
file_access:
  - rule_id: "directory_protection"
    enabled: true
    rules:
      - path: "/home/<USER>/test/restricted_dir"
        sub_rule_id: "dir_access"
        access: "write"
        dir: true
        action: "block"
        allowed:
          - process: "*"
            uid: 1000
```

**示例3: 审计模式监控**

```yaml
file_access:
  - rule_id: "audit_monitoring"
    enabled: true
    rules:
      - path: "/var/log"
        sub_rule_id: "log_audit"
        access: "write"
        dir: true
        action: "audit"  # 仅记录，不阻止
        allowed: []
```

**示例4: 文件系统保护**

```yaml
filesystem_protect:
  - rule_id: "global_protection"
    enabled: true
    action: "block"
    allowed:
      - path: "/tmp"
        process_path: "*"
        uid: 4294967295
        is_dir: true
      - path: "/home/<USER>/workspace"
        process_path: "*"
        uid: 1000
        is_dir: true
```

### 命令行使用

#### 可用命令

```bash
# 启动监控
sudo fim-ctl start [config_file]

# 测试配置
sudo fim-ctl test [config_file]

# 显示帮助
fim-ctl help

# 显示版本
fim-ctl version
```

**命令说明**:
- `start`: 启动 FIM 监控，前台运行，按 Ctrl+C 停止
- `test`: 验证配置文件格式和规则有效性
- `help`: 显示使用帮助
- `version`: 显示版本信息

#### 使用示例

**测试配置文件**

```bash
# 使用示例配置测试
sudo fim-ctl test examples/configs/basic_example.yaml

# 测试自定义配置
sudo fim-ctl test /etc/fim/policy.yaml
```

**启动监控**

```bash
# 使用默认配置启动
sudo fim-ctl start

# 使用自定义配置启动
sudo fim-ctl start /etc/fim/policy.yaml

# 使用示例配置启动
sudo fim-ctl start examples/configs/basic_example.yaml
```

**查看运行状态**

```bash
# 查看帮助信息
fim-ctl help

# 查看版本信息
fim-ctl version
```

### 实际应用场景

#### 系统文件保护

```yaml
file_access:
  - rule_id: "system_protection"
    enabled: true
    rules:
      - path: "/etc/passwd"
        sub_rule_id: "passwd_protection"
        access: "write"
        dir: false
        action: "block"
        allowed:
          - process: "/usr/sbin/useradd"
            uid: 0
          - process: "/usr/sbin/userdel"
            uid: 0
```

#### 用户数据保护

```yaml
file_access:
  - rule_id: "user_data_protection"
    enabled: true
    rules:
      - path: "/home/<USER>/sensitive"
        sub_rule_id: "sensitive_data"
        access: "read"
        dir: true
        action: "block"
        allowed:
          - process: "*"
            uid: 1000  # 仅允许文件所有者
```

#### 应用程序保护

```yaml
file_access:
  - rule_id: "app_protection"
    enabled: true
    rules:
      - path: "/var/www/html/config.php"
        sub_rule_id: "web_config"
        access: "write"
        dir: false
        action: "block"
        allowed:
          - process: "/usr/sbin/apache2"
            uid: 33  # www-data 用户

filesystem_protect:
  - rule_id: "web_protection"
    enabled: true
    action: "audit"  # 审计模式，记录所有写操作
    allowed:
      - path: "/var/www/uploads"
        process_path: "/usr/sbin/apache2"
        uid: 33
        is_dir: true
```

## 系统设计

### 整体架构

FIM 系统由用户空间控制程序和内核空间 eBPF 程序组成：

```
┌─────────────────────────────────────────────────────────────┐
│                      用户空间                                │
├─────────────────────────────────────────────────────────────┤
│  fim-ctl                                                    │
│  ├── main.c           - 主程序和命令处理                      │
│  ├── config.c         - YAML 配置解析                       │
│  ├── ebpf_manager.c   - eBPF 程序管理                       │
│  ├── event_handler.c  - 事件处理和日志                       │
│  └── utils.c          - 工具函数                            │
├─────────────────────────────────────────────────────────────┤
│                      内核空间                                │
├─────────────────────────────────────────────────────────────┤
│  fim_lsm.c - eBPF LSM 程序                                  │
│  ├── LSM 钩子 (file_open, inode_*)                          │
│  ├── 规则匹配和访问控制                                       │
│  ├── Ring Buffer 事件通信                                   │
│  └── eBPF Maps (规则存储)                                   │
└─────────────────────────────────────────────────────────────┘
```

### eBPF 程序设计

#### LSM 钩子实现

FIM 使用以下 LSM 钩子，每个钩子都支持 block 和 audit 两种动作：

1. **file_open**: 监控文件打开操作，区分读写访问
2. **inode_mkdir**: 监控目录创建，支持阻止/审计
3. **inode_rmdir**: 监控目录删除，支持阻止/审计
4. **inode_unlink**: 监控文件删除，支持阻止/审计
5. **inode_rename**: 监控文件重命名，支持阻止/审计
6. **inode_setattr**: 监控文件属性修改
7. **inode_link**: 监控硬链接创建
8. **inode_symlink**: 监控符号链接创建

#### 核心数据结构

```c
// 文件访问规则键
struct file_access_key {
    __u32 dev;          // 设备号
    __u32 inode;        // inode 号
    __u32 access_type;  // 访问类型 (1=读, 2=写)
};

// 文件访问规则值
struct file_access_value {
    __u32 rule_enabled;   // 规则启用状态
    __u32 action;         // 动作 (1=审计, 2=阻止)
    __u32 has_exceptions; // 是否有例外规则
    char rule_id[64];     // 规则ID
};

// 日志事件结构
struct fim_log_event {
    __u64 timestamp;    // 时间戳
    __u32 pid;          // 进程ID
    __u32 uid;          // 用户ID
    __u32 operation;    // 操作类型
    __u32 action;       // 执行动作
    __u32 dev;          // 设备号
    __u32 inode;        // inode号
    char comm[16];      // 进程名
    char filename[64];  // 文件名
};
```

#### eBPF Maps

```c
// 文件访问规则映射
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __type(key, struct file_access_key);
    __type(value, struct file_access_value);
    __uint(max_entries, 4096);
} file_access_map SEC(".maps");

// 日志事件环形缓冲区
struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 1048576); // 1MB
} log_events SEC(".maps");

// inode 变更事件缓冲区
struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 262144); // 256KB
} inode_events SEC(".maps");
```

### 工作流程

#### 1. 初始化流程

```
启动 fim-ctl → 解析配置文件 → 加载 eBPF 程序 →
附加 LSM 钩子 → 更新 eBPF Maps → 启动事件处理
```

#### 2. 运行时监控

```
文件操作 → LSM 钩子触发 → 查找规则 → 检查权限 →
执行动作 (允许/阻止) → 记录事件到 Ring Buffer
```

#### 3. 事件处理

```
Ring Buffer 事件 → 用户空间轮询 → 解析事件 →
格式化输出 → 更新统计信息
```

### 规则匹配逻辑

#### 配置解析

1. **YAML 解析**: 解析配置文件中的 file_access 规则
2. **路径解析**: 获取监控路径的设备号和 inode
3. **规则构建**: 构造 file_access_key 和 file_access_value
4. **Map 更新**: 将规则写入 eBPF file_access_map

#### 运行时匹配

```c
// 核心匹配逻辑
static __always_inline int check_file_access_by_inode(
    struct dentry *dentry, __u32 access_type,
    __u32 process_dev, __u32 process_inode, __u32 uid) {

    // 1. 获取文件的设备号和 inode
    struct inode *inode_ptr = BPF_CORE_READ(dentry, d_inode);
    __u32 dev = BPF_CORE_READ(inode_ptr, i_sb, s_dev);
    __u32 inode = BPF_CORE_READ(inode_ptr, i_ino);

    // 2. 构造查找键
    struct file_access_key key = {
        .dev = dev,
        .inode = inode,
        .access_type = access_type
    };

    // 3. 查找规则
    struct file_access_value *rule =
        bpf_map_lookup_elem(&file_access_map, &key);

    // 4. 检查权限
    if (rule && rule->rule_enabled) {
        // 检查进程权限
        if (rule->process_dev != DEVICE_ANY &&
            rule->process_inode != PROCESS_ANY) {
            if (rule->process_dev == process_dev &&
                rule->process_inode == process_inode) {
                return 0; // 允许
            }
        }

        // 检查用户权限
        if (rule->allowed_uid == UID_ANY ||
            rule->allowed_uid == uid) {
            return 0; // 允许
        }

        return rule->action; // 执行规则动作
    }

    return 0; // 无规则，允许
}
```

#### 异常处理

- **进程通配符**: `process: "*"` 表示任意进程
- **用户通配符**: `uid: 4294967295` 表示任意用户
- **目录递归**: 对目录规则检查父目录权限

### 性能特性

#### 内核态优化

- **Hash Map 查找**: O(1) 时间复杂度的规则匹配
- **Ring Buffer 通信**: 高效的内核-用户空间数据传输
- **最小化拷贝**: 直接在内核态处理大部分逻辑

#### 用户态优化

- **事件轮询**: 100ms 超时的非阻塞轮询
- **批量处理**: 一次轮询处理多个事件
- **内存管理**: 合理的资源分配和清理

#### 配置建议

- **规则数量**: 建议每个配置文件规则数 < 1000
- **监控范围**: 避免监控高频访问的系统目录
- **通配符使用**: 合理使用进程和用户通配符

### 事件统计

系统在运行时维护以下统计信息：

```c
// 在 event_handler.c 中定义
static unsigned long long total_events = 0;     // 总事件数
static unsigned long long blocked_events = 0;   // 阻止事件数
static unsigned long long monitored_events = 0; // 监控事件数
```

可通过程序输出查看统计信息，或使用以下命令检查系统状态：

```bash
# 查看 eBPF 程序
sudo bpftool prog show | grep fim

# 查看进程状态
ps aux | grep fim-ctl

# 查看系统日志
sudo dmesg | grep -i bpf
```

## 故障排除

### 常见问题

#### 1. 权限不足

**症状**: 提示需要 root 权限

**解决方案**:
```bash
# 使用 sudo 运行
sudo fim-ctl start config.yaml

# 检查当前用户权限
id
```

#### 2. eBPF 程序加载失败

**症状**: 启动时提示加载 eBPF 程序失败

**解决方案**:
```bash
# 检查内核版本 (需要 >= 5.8)
uname -r

# 检查 LSM 支持
cat /sys/kernel/security/lsm | grep bpf

# 查看内核日志
sudo dmesg | grep -i bpf
```

#### 3. 配置文件错误

**症状**: 配置测试失败

**解决方案**:
```bash
# 验证 YAML 语法
python3 -c "import yaml; yaml.safe_load(open('config.yaml'))"

# 检查文件路径是否存在
ls -la /path/to/monitored/file

# 使用示例配置测试
sudo fim-ctl test examples/configs/basic_example.yaml
```

#### 4. 规则不生效

**症状**: 文件操作未被阻止

**解决方案**:
```bash
# 检查文件 inode
stat /path/to/file

# 验证进程路径
which command_name
readlink -f /proc/self/exe

# 检查用户 ID
id -u
```

#### 5. 编译错误

**症状**: make 编译失败

**解决方案**:
```bash
# 检查依赖包
dpkg -l | grep -E "(clang|libbpf|libyaml)"

# 清理重新编译
make clean && make

# 检查编译器版本
clang --version
```

### 调试方法

#### 查看程序状态

```bash
# 查看进程状态
ps aux | grep fim-ctl

# 查看 eBPF 程序
sudo bpftool prog show | grep fim

# 查看 eBPF Maps
sudo bpftool map show | grep fim
```

#### 系统日志

```bash
# 查看内核日志
sudo dmesg | tail -50

# 查看 eBPF 相关日志
sudo dmesg | grep -i bpf

# 实时查看内核跟踪
sudo cat /sys/kernel/debug/tracing/trace_pipe
```

#### 配置验证

```bash
# 验证配置文件语法
sudo fim-ctl test config.yaml

# 检查监控路径
stat /path/to/monitored/file

# 验证进程权限
sudo -u username command
```

### 使用建议

#### 配置管理

**目录结构**:
```
/etc/fim/
├── policy.yaml          # 主配置文件
└── backup/
    └── policy.yaml.bak  # 配置备份
```

**配置原则**:
1. **最小权限**: 仅授予必要的访问权限
2. **分类管理**: 按系统、应用、用户分类配置规则
3. **测试验证**: 部署前使用 `test` 命令验证配置
4. **备份配置**: 修改前备份原配置文件

#### 部署策略

**渐进部署**:
```bash
# 1. 测试配置
sudo fim-ctl test /etc/fim/policy.yaml

# 2. 小范围测试
sudo fim-ctl start /etc/fim/test-policy.yaml

# 3. 生产部署
sudo fim-ctl start /etc/fim/policy.yaml
```

**监控重点**:
- 系统关键文件 (`/etc/passwd`, `/etc/shadow`)
- 应用配置文件
- 用户敏感数据
- 日志文件

### 系统集成

#### systemd 服务

项目提供了 systemd 服务文件：

```bash
# 安装服务
sudo cp scripts/fim.service /etc/systemd/system/
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable fim
sudo systemctl start fim

# 查看状态
sudo systemctl status fim
```

#### 日志管理

FIM 输出到标准输出，可通过以下方式管理日志：

```bash
# 查看 systemd 日志
sudo journalctl -u fim -f

# 重定向到文件
sudo fim-ctl start config.yaml > /var/log/fim.log 2>&1
```

#### 开机自启

```bash
# 启用开机自启
sudo systemctl enable fim

# 禁用开机自启
sudo systemctl disable fim
```

### 安全注意事项

#### 权限要求

FIM 需要 root 权限运行，因为：
- 加载 eBPF 程序需要 CAP_BPF 权限
- 附加 LSM 钩子需要 CAP_SYS_ADMIN 权限
- 访问某些系统文件需要 root 权限

#### 配置文件安全

```bash
# 保护配置文件
sudo chown root:root /etc/fim/policy.yaml
sudo chmod 600 /etc/fim/policy.yaml

# 备份配置
sudo cp /etc/fim/policy.yaml /etc/fim/policy.yaml.bak
```

#### 运行安全

- 仅在受信任的环境中运行
- 定期检查和更新配置规则
- 监控系统日志中的异常信息
- 避免在生产环境中使用过于宽泛的规则

---

**版本**: FIM v2.0.0
**文档更新**: 2024年
**适用范围**: 使用指南和系统设计

## 项目结构

```
FIM/
├── src/
│   ├── ebpf/           # eBPF 内核程序
│   │   ├── fim_lsm.c   # LSM 钩子实现
│   │   └── fim_common.h # eBPF 公共定义
│   ├── userspace/      # 用户空间程序
│   │   ├── main.c      # 主程序入口
│   │   ├── config.c    # 配置解析
│   │   ├── ebpf_manager.c # eBPF 管理
│   │   ├── event_handler.c # 事件处理
│   │   ├── utils.c     # 工具函数
│   │   └── fim_common.h # 用户空间公共定义
│   └── include/        # 共享头文件
│       └── fim_shared.h # 内核-用户空间共享定义
├── config/             # 默认配置文件
├── examples/           # 配置示例
│   └── configs/
│       └── basic_example.yaml
├── scripts/            # 安装和服务脚本
│   ├── install.sh      # 安装脚本
│   ├── uninstall.sh    # 卸载脚本
│   └── fim.service     # systemd 服务文件
├── bin/                # 编译后的二进制文件
└── build/              # 编译中间文件
```

## 技术限制

### 当前支持的功能

- ✅ 文件访问控制 (读/写分离)
- ✅ 进程和用户权限检查
- ✅ 实时事件记录
- ✅ 动态 inode 跟踪
- ✅ YAML 配置文件
- ✅ 审计模式 (audit action)
- ✅ 阻止模式 (block action)
- ✅ 文件系统保护 (filesystem_protect)
- ✅ 例外规则处理

### 当前不支持的功能

- ❌ 允许列表 (allow_list) - 已实现解析但未启用
- ❌ 网络接口或 REST API
- ❌ 插件系统
- ❌ 配置热重载
- ❌ 分布式部署

### 已知限制

- 需要 Linux 内核 >= 5.8
- 必须以 root 权限运行
- 配置更改需要重启程序
- 高频操作可能影响系统性能



---

**版本**: FIM v2.0.0
**更新日期**: 2024年
**文档语言**: 简体中文
